const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { createUmi } = require('@metaplex-foundation/umi-bundle-defaults');
const { keypairIdentity, publicKey } = require('@metaplex-foundation/umi');
const { transfer, mplCore } = require('@metaplex-foundation/mpl-core');
const fs = require('fs-extra');

async function transferCoreNFTsToClient() {
  console.log('🔄 Transferring Core NFTs to Client Wallet\n');
  
  // Load wallet
  const envContent = await fs.readFile('.env', 'utf8');
  const privateKeyMatch = envContent.match(/PRIVATE_KEY=(.+)/);
  if (!privateKeyMatch) {
    throw new Error('PRIVATE_KEY not found in .env file');
  }
  const privateKeyArray = JSON.parse(privateKeyMatch[1]);
  const wallet = Keypair.fromSecretKey(new Uint8Array(privateKeyArray));
  
  // Setup UMI
  const umi = createUmi('https://api.devnet.solana.com');
  umi.use(mplCore());
  umi.use(keypairIdentity({
    publicKey: publicKey(wallet.publicKey.toBase58()),
    secretKey: wallet.secretKey
  }));

  // Client address
  const clientAddress = 'FmYrSSXT8AXfEW7HTjrNZXfWeAZ8375j3F2z6KFkseS7';
  console.log(`🎯 Client Address: ${clientAddress}\n`);

  // Load Core NFT results
  const results = await fs.readJson('mint-results-1753969525776.json');
  const coreNFTs = results.results.filter(r => r.success);
  
  console.log(`📦 Found ${coreNFTs.length} Core NFTs to transfer\n`);

  const transferResults = [];
  let successCount = 0;
  let failCount = 0;

  for (let i = 0; i < coreNFTs.length; i++) {
    const nft = coreNFTs[i];
    const nftAddress = nft.mintAddress;
    
    try {
      console.log(`⏳ Transferring NFT ${i + 1}/${coreNFTs.length}: ${nftAddress}`);
      
      const startTime = Date.now();
      
      // Transfer the Core NFT
      const transferTx = transfer(umi, {
        asset: publicKey(nftAddress),
        newOwner: publicKey(clientAddress),
      });

      const signature = await transferTx.sendAndConfirm(umi);
      const timeTaken = (Date.now() - startTime) / 1000;

      console.log(`✅ NFT ${i + 1} transferred successfully`);
      console.log(`   Signature: ${signature}`);
      console.log(`   Time: ${timeTaken.toFixed(2)}s\n`);

      transferResults.push({
        success: true,
        nftNumber: i + 1,
        nftAddress: nftAddress,
        signature: signature,
        timeTaken: timeTaken,
        transferredTo: clientAddress
      });

      successCount++;

      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error(`❌ Failed to transfer NFT ${i + 1}: ${nftAddress}`);
      console.error(`   Error: ${error.message}\n`);
      
      transferResults.push({
        success: false,
        nftNumber: i + 1,
        nftAddress: nftAddress,
        error: error.message
      });

      failCount++;
    }
  }

  const finalResults = {
    totalNFTs: coreNFTs.length,
    successfulTransfers: successCount,
    failedTransfers: failCount,
    clientAddress: clientAddress,
    originalCost: results.averageCostPerNFT,
    originalTotalCost: results.totalCost,
    transferResults: transferResults,
    timestamp: Date.now()
  };

  // Save transfer results
  const transferFile = `core-nft-transfers-${Date.now()}.json`;
  await fs.writeJson(transferFile, finalResults, { spaces: 2 });

  console.log('\n🎉 Transfer Process Complete!');
  console.log(`📊 Results:`);
  console.log(`   ✅ Successful transfers: ${successCount}/${coreNFTs.length}`);
  console.log(`   ❌ Failed transfers: ${failCount}/${coreNFTs.length}`);
  console.log(`   💰 Original cost per NFT: ${results.averageCostPerNFT.toFixed(6)} SOL`);
  console.log(`   🎯 Client Address: ${clientAddress}`);
  console.log(`   📄 Transfer results saved: ${transferFile}\n`);

  if (successCount > 0) {
    console.log('🔗 Sample transferred NFT addresses:');
    transferResults.filter(r => r.success).slice(0, 5).forEach(nft => {
      console.log(`   ${nft.nftAddress} - https://explorer.solana.com/address/${nft.nftAddress}?cluster=devnet`);
    });
  }

  return { finalResults, transferFile };
}

// Run if called directly
if (require.main === module) {
  transferCoreNFTsToClient().catch(console.error);
}

module.exports = { transferCoreNFTsToClient };
