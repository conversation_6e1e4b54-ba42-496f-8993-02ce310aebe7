#!/usr/bin/env node

// ========================================
// 🚀 SIMPLE CLIENT NFT MINTER
// ========================================
// 
// This script reads your configuration and mints NFTs
// You don't need to modify this file!
// Just run: npm start
//
// ========================================

const { Connection, Keypair } = require('@solana/web3.js');
const { createUmi } = require('@metaplex-foundation/umi-bundle-defaults');
const { keypairIdentity, publicKey, generateSigner } = require('@metaplex-foundation/umi');
const { create, mplCore } = require('@metaplex-foundation/mpl-core');
const fs = require('fs-extra');
const config = require('./client-config');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function validateConfig() {
  log('\n🔍 Validating your configuration...', 'cyan');
  
  // Check required fields
  if (!config.WALLET_PRIVATE_KEY || !Array.isArray(config.WALLET_PRIVATE_KEY)) {
    throw new Error('❌ WALLET_PRIVATE_KEY must be an array. Please check client-config.js');
  }
  
  if (!config.CLIENT_WALLET_ADDRESS || typeof config.CLIENT_WALLET_ADDRESS !== 'string') {
    throw new Error('❌ CLIENT_WALLET_ADDRESS must be a valid wallet address. Please check client-config.js');
  }
  
  if (!config.NUMBER_OF_NFTS || config.NUMBER_OF_NFTS < 1) {
    throw new Error('❌ NUMBER_OF_NFTS must be at least 1. Please check client-config.js');
  }
  
  log('✅ Configuration looks good!', 'green');
}

async function setupWallet() {
  log('\n🔑 Setting up your wallet...', 'cyan');
  
  try {
    const wallet = Keypair.fromSecretKey(new Uint8Array(config.WALLET_PRIVATE_KEY));
    log(`✅ Wallet loaded: ${wallet.publicKey.toBase58()}`, 'green');
    
    // Set RPC URL based on network
    const rpcUrl = config.ADVANCED.RPC_URL || 
      (config.NETWORK === 'mainnet-beta' ? 
        'https://api.mainnet-beta.solana.com' : 
        'https://api.devnet.solana.com');
    
    const connection = new Connection(rpcUrl, 'confirmed');

    // Try to get balance with retry
    let balance, balanceSOL;
    try {
      balance = await connection.getBalance(wallet.publicKey);
      balanceSOL = balance / 1e9;
    } catch (error) {
      log('⚠️  Network connection issue, retrying...', 'yellow');
      await new Promise(resolve => setTimeout(resolve, 2000));
      balance = await connection.getBalance(wallet.publicKey);
      balanceSOL = balance / 1e9;
    }
    
    log(`💰 Wallet balance: ${balanceSOL.toFixed(6)} SOL`, 'green');
    
    // Check if enough balance
    const estimatedCost = config.NUMBER_OF_NFTS * config.MAX_COST_PER_NFT;
    if (balanceSOL < estimatedCost) {
      log(`⚠️  Warning: You might need more SOL. Estimated cost: ${estimatedCost.toFixed(6)} SOL`, 'yellow');
    }
    
    return { wallet, connection, rpcUrl };
  } catch (error) {
    throw new Error(`❌ Failed to setup wallet: ${error.message}`);
  }
}

async function mintNFTs(wallet, rpcUrl) {
  log(`\n🎨 Starting to mint ${config.NUMBER_OF_NFTS} NFTs...`, 'cyan');
  log(`🎯 Sending to: ${config.CLIENT_WALLET_ADDRESS}`, 'cyan');
  log(`🌐 Network: ${config.NETWORK}`, 'cyan');
  
  // Setup UMI
  const umi = createUmi(rpcUrl);
  umi.use(mplCore());
  umi.use(keypairIdentity({
    publicKey: publicKey(wallet.publicKey.toBase58()),
    secretKey: wallet.secretKey
  }));
  
  const results = [];
  let totalCost = 0;
  const startTime = Date.now();
  
  for (let i = 1; i <= config.NUMBER_OF_NFTS; i++) {
    try {
      log(`\n⏳ Minting NFT ${i}/${config.NUMBER_OF_NFTS}...`, 'yellow');
      
      const nftStartTime = Date.now();
      const initialBalance = await umi.rpc.getBalance(umi.identity.publicKey);
      
      // Generate asset
      const asset = generateSigner(umi);
      
      // Create metadata
      const metadata = {
        name: `${config.NFT_TEMPLATE.name} #${i}`,
        description: config.NFT_TEMPLATE.description,
        image: config.NFT_TEMPLATE.image,
        attributes: [
          ...config.NFT_TEMPLATE.attributes,
          { trait_type: "Number", value: i.toString() },
          { trait_type: "Collection", value: config.COLLECTION_NAME }
        ]
      };
      
      const metadataUri = `data:application/json;base64,${Buffer.from(JSON.stringify(metadata)).toString('base64')}`;
      
      // Create NFT directly to client wallet
      const createTx = await create(umi, {
        asset,
        name: metadata.name,
        uri: metadataUri,
        owner: publicKey(config.CLIENT_WALLET_ADDRESS)
      });
      
      const signature = await createTx.sendAndConfirm(umi);
      
      // Calculate cost
      const finalBalance = await umi.rpc.getBalance(umi.identity.publicKey);
      const cost = Number(initialBalance.basisPoints - finalBalance.basisPoints) / 1e9;
      const timeTaken = (Date.now() - nftStartTime) / 1000;
      
      totalCost += cost;
      
      log(`✅ NFT ${i} minted successfully!`, 'green');
      log(`   Address: ${asset.publicKey}`, 'white');
      log(`   Cost: ${cost.toFixed(6)} SOL`, 'white');
      log(`   Time: ${timeTaken.toFixed(2)}s`, 'white');
      
      results.push({
        success: true,
        number: i,
        address: asset.publicKey.toString(),
        signature: signature.toString(),
        cost: cost,
        timeTaken: timeTaken,
        metadata: metadata,
        explorerUrl: `https://explorer.solana.com/address/${asset.publicKey}?cluster=${config.NETWORK}`
      });
      
      // Delay between mints
      if (i < config.NUMBER_OF_NFTS) {
        await new Promise(resolve => setTimeout(resolve, config.ADVANCED.MINT_DELAY));
      }
      
    } catch (error) {
      log(`❌ Failed to mint NFT ${i}: ${error.message}`, 'red');
      results.push({
        success: false,
        number: i,
        error: error.message
      });
    }
  }
  
  const totalTime = (Date.now() - startTime) / 1000;
  const successful = results.filter(r => r.success).length;
  const failed = results.length - successful;
  const avgCost = successful > 0 ? totalCost / successful : 0;
  const nftsPerHour = totalTime > 0 ? Math.round((successful / totalTime) * 3600) : 0;
  
  return {
    successful,
    failed,
    totalCost,
    avgCost,
    totalTime,
    nftsPerHour,
    results
  };
}

async function generateReport(mintResults) {
  if (!config.ADVANCED.GENERATE_REPORT) return;
  
  log('\n📊 Generating your NFT report...', 'cyan');
  
  const timestamp = Date.now();
  const successful = mintResults.successful;
  const avgCost = mintResults.avgCost;
  const budgetMet = avgCost <= config.MAX_COST_PER_NFT;
  
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your NFT Collection - Minting Report</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 25px; border-radius: 10px; text-align: center; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2.5em; font-weight: bold; color: #667eea; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 1.1em; }
        .success { color: #10b981; }
        .warning { color: #f59e0b; }
        .info-box { background: white; padding: 25px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .wallet-address { font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 8px; word-break: break-all; }
        .nft-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .nft-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .nft-address { font-family: monospace; font-size: 0.9em; color: #666; word-break: break-all; }
        .explorer-btn { background: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 6px; text-decoration: none; display: inline-block; margin-top: 10px; }
        .explorer-btn:hover { background: #5a67d8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎉 Your NFT Collection is Ready!</h1>
        <p>${successful} NFTs successfully minted and sent to your wallet</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">${successful}</div>
            <div class="stat-label">NFTs Minted</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${avgCost.toFixed(6)}</div>
            <div class="stat-label">SOL per NFT</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${mintResults.totalCost.toFixed(6)}</div>
            <div class="stat-label">Total Cost</div>
        </div>
        <div class="stat-card">
            <div class="stat-number ${budgetMet ? 'success' : 'warning'}">${budgetMet ? '✅' : '⚠️'}</div>
            <div class="stat-label">Budget ${budgetMet ? 'Met' : 'Status'}</div>
        </div>
    </div>
    
    <div class="info-box">
        <h2>📍 Your Wallet Address</h2>
        <div class="wallet-address">${config.CLIENT_WALLET_ADDRESS}</div>
        <p><strong>Network:</strong> ${config.NETWORK}</p>
        <p><strong>Collection:</strong> ${config.COLLECTION_NAME}</p>
    </div>
    
    <div class="info-box">
        <h2>🎨 Your NFTs</h2>
        <div class="nft-grid">
            ${mintResults.results.filter(r => r.success).map(nft => `
                <div class="nft-card">
                    <h3>${nft.metadata.name}</h3>
                    <div class="nft-address">${nft.address}</div>
                    <p><strong>Cost:</strong> ${nft.cost.toFixed(6)} SOL</p>
                    <a href="${nft.explorerUrl}" target="_blank" class="explorer-btn">View on Explorer</a>
                </div>
            `).join('')}
        </div>
    </div>
    
    <div class="info-box" style="text-align: center;">
        <h2>🎯 Next Steps</h2>
        <p>1. Check your wallet - the NFTs should appear there</p>
        <p>2. Make sure your wallet is connected to <strong>${config.NETWORK}</strong></p>
        <p>3. Click "View on Explorer" links above to verify ownership</p>
        <p>4. Refresh your wallet if NFTs don't appear immediately</p>
    </div>
</body>
</html>`;

  const reportFile = `client-nft-report-${timestamp}.html`;
  await fs.writeFile(reportFile, htmlContent);
  
  log(`✅ Report saved: ${reportFile}`, 'green');
  return reportFile;
}

async function main() {
  try {
    log('🚀 CLIENT NFT MINTER STARTING...', 'blue');
    log('=====================================', 'blue');
    
    // Validate configuration
    await validateConfig();
    
    // Setup wallet
    const { wallet, connection, rpcUrl } = await setupWallet();
    
    // Mint NFTs
    const results = await mintNFTs(wallet, rpcUrl);
    
    // Show results
    log('\n🎉 MINTING COMPLETED!', 'green');
    log('=====================================', 'green');
    log(`✅ Successful: ${results.successful}/${config.NUMBER_OF_NFTS}`, 'green');
    log(`❌ Failed: ${results.failed}/${config.NUMBER_OF_NFTS}`, results.failed > 0 ? 'red' : 'green');
    log(`💰 Total cost: ${results.totalCost.toFixed(6)} SOL`, 'cyan');
    log(`📊 Average cost: ${results.avgCost.toFixed(6)} SOL per NFT`, 'cyan');
    log(`⏱️  Total time: ${(results.totalTime / 60).toFixed(1)} minutes`, 'cyan');
    log(`🎯 Sent to: ${config.CLIENT_WALLET_ADDRESS}`, 'cyan');
    
    // Budget check
    if (results.avgCost <= config.MAX_COST_PER_NFT) {
      log(`✅ Budget target met! (≤${config.MAX_COST_PER_NFT} SOL per NFT)`, 'green');
    } else {
      log(`⚠️  Budget exceeded (target: ≤${config.MAX_COST_PER_NFT} SOL per NFT)`, 'yellow');
    }
    
    // Generate report
    const reportFile = await generateReport(results);
    if (reportFile) {
      log(`📊 Report generated: ${reportFile}`, 'green');
    }
    
    log('\n🎯 SUCCESS! Check your wallet for the NFTs!', 'green');
    
  } catch (error) {
    log(`\n❌ ERROR: ${error.message}`, 'red');
    log('\n💡 Please check your client-config.js file and try again.', 'yellow');
    process.exit(1);
  }
}

// Run the minter
main();
