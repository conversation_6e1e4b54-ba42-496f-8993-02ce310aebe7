#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import * as fs from 'fs-extra';
import * as path from 'path';
import { <PERSON>Key } from '@solana/web3.js';

import { NFTMinter } from './nft-minter';
import { CoreNFTMinter } from './core-nft-minter';
import { Config } from './config';
import { Logger } from './utils/logger';
import { NFTConfig, MintingOptions } from './types';

const program = new Command();
const logger = Logger.getInstance();
const config = Config.getInstance();

async function generateTestNFTs(count: number): Promise<NFTConfig[]> {
  const testNFTs: NFTConfig[] = [];
  
  // Create test assets directory if it doesn't exist
  const testAssetsDir = path.join(process.cwd(), 'test-assets');
  await fs.ensureDir(testAssetsDir);
  
  // Create a simple test image if it doesn't exist
  const testImagePath = path.join(testAssetsDir, 'test-nft.png');
  if (!await fs.pathExists(testImagePath)) {
    // Create a simple 1x1 PNG for testing
    const pngBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x57, 0x63, 0xF8, 0x0F, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xCD, 0x90, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    await fs.writeFile(testImagePath, pngBuffer);
  }
  
  for (let i = 0; i < count; i++) {
    testNFTs.push({
      name: `Regular NFT #${i + 1}`, // Regular NFT name
      symbol: 'REG', // Regular NFT symbol
      description: `Regular NFT #${i + 1} with Token Metadata standard`, // Description
      sellerFeeBasisPoints: 500, // 5% royalties
      creators: [
        {
          address: new PublicKey('11111111111111111111111111111112'), // System program as placeholder
          share: 100,
          verified: false
        }
      ],
      attributes: [
        { trait_type: 'Type', value: 'Regular' },
        { trait_type: 'ID', value: (i + 1).toString() },
        { trait_type: 'Standard', value: 'Token Metadata' }
      ],
      collection: {
        name: 'Regular NFT Collection',
        family: 'Token Metadata'
      },
      imagePath: testImagePath,
      isMutable: false,
      maxSupply: 1
    });
  }
  
  return testNFTs;
}

program
  .name('solana-nft-minter')
  .description('Enhanced Solana NFT batch minting tool')
  .version('1.0.0');

program
  .command('setup')
  .description('Setup project structure and example files')
  .action(async () => {
    try {
      console.log(chalk.blue.bold('🛠️  Setting up project structure\n'));
      
      // Create directories
      const dirs = ['assets', 'config', 'logs', 'test-assets'];
      for (const dir of dirs) {
        await fs.ensureDir(dir);
        console.log(chalk.green(`✅ Created directory: ${dir}`));
      }
      
      // Create example .env file
      const envPath = '.env';
      if (!await fs.pathExists(envPath)) {
        await fs.copy('.env.example', envPath);
        console.log(chalk.green('✅ Created .env file from example'));
      }
      
      console.log(chalk.cyan('\n📋 Next steps:'));
      console.log(chalk.cyan('1. Edit .env file with your configuration'));
      console.log(chalk.cyan('2. Add your images to the assets/ directory'));
      console.log(chalk.cyan('3. Run: npm run dev validate --keypair <your-keypair>'));
      console.log(chalk.cyan('4. Run: npm run dev mint --test'));
      
    } catch (error) {
      console.error(chalk.red('❌ Setup error:'), error);
      process.exit(1);
    }
  });

program
  .command('mint')
  .description('Mint NFTs in batches')
  .option('-n, --network <network>', 'Solana network (devnet|mainnet-beta)', 'devnet')
  .option('-b, --batch-size <size>', 'Number of NFTs to mint per batch', '25')
  .option('-k, --keypair <path>', 'Path to wallet keypair file')
  .option('-c, --client-address <address>', 'Client wallet address to receive NFTs')
  .option('--test', 'Run in test mode with sample data')
  .action(async (options) => {
    try {
      console.log(chalk.blue.bold('🚀 Enhanced Solana NFT Minter\n'));
      
      // Load wallet
      let privateKey: string;
      if (options.keypair) {
        const keypairData = await fs.readJSON(options.keypair);
        privateKey = JSON.stringify(keypairData);
      } else if (process.env.SOLANA_PRIVATE_KEY) {
        privateKey = process.env.SOLANA_PRIVATE_KEY;
      } else {
        console.error(chalk.red('❌ No wallet keypair provided. Use --keypair or set SOLANA_PRIVATE_KEY'));
        process.exit(1);
      }
      
      // Update minting options from CLI arguments
      const mintingOptions: MintingOptions = {
        ...config.mintingOptions,
        network: options.network as 'devnet' | 'mainnet-beta',
        batchSize: parseInt(options.batchSize)
      };
      
      // Initialize CORE NFT minter (meets budget + goes to client wallet)
      const minter = new CoreNFTMinter(privateKey, mintingOptions);
      
      // Validate wallet
      const spinner = ora('Validating wallet...').start();
      const walletValidation = await minter.validateWallet();
      
      if (!walletValidation.isValid) {
        spinner.fail(chalk.red(`Wallet validation failed: ${walletValidation.errors.join(', ')}`));
        process.exit(1);
      }
      
      spinner.succeed(chalk.green(`Wallet validated. Balance: ${walletValidation.balance.toFixed(6)} SOL`));
      
      // Load NFT configurations
      let nftConfigs: NFTConfig[];
      
      if (options.test) {
        nftConfigs = await generateTestNFTs(mintingOptions.batchSize);
        console.log(chalk.cyan(`📋 Generated ${nftConfigs.length} test NFTs`));
      } else {
        console.error(chalk.red('❌ Only test mode is currently supported. Use --test flag'));
        process.exit(1);
      }
      
      // Estimate costs
      const costEstimate = await minter.estimateCosts(nftConfigs.length);
      console.log(chalk.yellow('\n💰 Cost Estimation:'));
      console.log(chalk.yellow(`  • Total estimated cost: ${costEstimate.totalEstimatedCost.toFixed(6)} SOL`));
      console.log(chalk.yellow(`  • Cost per NFT: ${costEstimate.costPerNFT.toFixed(6)} SOL`));
      
      // Check if wallet has sufficient balance
      if (walletValidation.balance < costEstimate.totalEstimatedCost) {
        console.error(chalk.red(`❌ Insufficient balance. Need ${costEstimate.totalEstimatedCost.toFixed(6)} SOL, have ${walletValidation.balance.toFixed(6)} SOL`));
        process.exit(1);
      }
      
      // Confirm before proceeding
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: `Proceed with minting ${nftConfigs.length} test NFTs on ${options.network}?`,
          default: false
        }
      ]);
      
      if (!confirm) {
        console.log(chalk.yellow('Operation cancelled'));
        process.exit(0);
      }
      
      // Start minting process
      console.log(chalk.green('\n🎯 Starting minting process...\n'));
      
      const result = await minter.batchMintCoreNFTs(nftConfigs, mintingOptions, options.clientAddress);
      
      // Display results
      console.log(chalk.green.bold('\n🎉 Minting completed!\n'));
      console.log(chalk.cyan('📊 Results Summary:'));
      console.log(chalk.green(`  ✅ Successful mints: ${result.successfulMints}`));
      console.log(chalk.red(`  ❌ Failed mints: ${result.failedMints}`));
      console.log(chalk.blue(`  💰 Total cost: ${result.totalCost.toFixed(6)} SOL`));
      console.log(chalk.blue(`  📈 Average cost per NFT: ${result.averageCostPerNFT.toFixed(6)} SOL`));
      console.log(chalk.blue(`  ⏱️  Total time: ${result.totalTime.toFixed(2)} seconds`));
      
      const nftsPerHour = (result.successfulMints / result.totalTime) * 3600;
      console.log(chalk.blue(`  🚀 Performance: ${Math.round(nftsPerHour)} NFTs/hour`));
      
      // Check if performance targets are met
      if (result.averageCostPerNFT <= 0.009) {
        console.log(chalk.green('✅ Cost target achieved: ≤ 0.009 SOL per NFT'));
      } else {
        console.log(chalk.yellow('⚠️  Cost target not met: > 0.009 SOL per NFT'));
      }
      
      if (nftsPerHour >= 1000) {
        console.log(chalk.green('✅ Performance target achieved: ≥ 1000 NFTs/hour'));
      } else {
        console.log(chalk.yellow('⚠️  Performance target not met: < 1000 NFTs/hour'));
      }
      
      // Save results to file
      const resultsFile = `mint-results-${Date.now()}.json`;
      await fs.writeJSON(resultsFile, result, { spaces: 2 });
      console.log(chalk.cyan(`\n📄 Results saved to: ${resultsFile}`));
      
      // Display successful mints
      if (result.successfulMints > 0) {
        console.log(chalk.green('\n🎨 Successfully minted NFTs:'));
        result.results
          .filter(r => r.success)
          .slice(0, 5) // Show first 5
          .forEach((r, index) => {
            const explorerUrl = mintingOptions.network === 'devnet' 
              ? `https://explorer.solana.com/address/${r.mintAddress}?cluster=devnet`
              : `https://explorer.solana.com/address/${r.mintAddress}`;
            console.log(chalk.green(`  ${index + 1}. ${r.mintAddress}`));
            console.log(chalk.gray(`     ${explorerUrl}`));
          });
        
        if (result.successfulMints > 5) {
          console.log(chalk.green(`  ... and ${result.successfulMints - 5} more`));
        }
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Fatal error:'), error);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();
