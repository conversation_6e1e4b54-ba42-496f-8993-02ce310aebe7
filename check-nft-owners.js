const { Connection, PublicKey } = require('@solana/web3.js');
const { createUmi } = require('@metaplex-foundation/umi-bundle-defaults');
const { publicKey } = require('@metaplex-foundation/umi');
const { fetchAsset, mplCore } = require('@metaplex-foundation/mpl-core');
const fs = require('fs-extra');

async function checkNFTOwners() {
  console.log('🔍 Checking NFT ownership...\n');
  
  // Setup connection
  const connection = new Connection('https://api.devnet.solana.com');
  const umi = createUmi('https://api.devnet.solana.com');
  umi.use(mplCore());
  
  // Load results
  const results = await fs.readJson('mint-results-1753971544026.json');
  
  const mintingWallet = '6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq';
  const clientWallet = 'FmYrSSXT8AXfEW7HTjrNZXfWeAZ8375j3F2z6KFkseS7';
  
  console.log(`🏦 Minting Wallet: ${mintingWallet}`);
  console.log(`👤 Client Wallet: ${clientWallet}\n`);
  
  // Check first 5 NFTs
  const nftsToCheck = results.results.slice(0, 5);
  
  for (let i = 0; i < nftsToCheck.length; i++) {
    const nft = nftsToCheck[i];
    const assetAddress = nft.mintAddress;
    
    try {
      console.log(`🔍 Checking NFT ${i + 1}: ${assetAddress}`);
      
      // Fetch Core NFT asset
      const asset = await fetchAsset(umi, publicKey(assetAddress));
      const owner = asset.owner.toString();
      
      console.log(`   Owner: ${owner}`);
      
      if (owner === clientWallet) {
        console.log(`   ✅ CORRECTLY owned by CLIENT`);
      } else if (owner === mintingWallet) {
        console.log(`   ❌ Still owned by MINTING WALLET`);
      } else {
        console.log(`   ⚠️  Owned by UNKNOWN wallet`);
      }
      
      console.log(`   Explorer: https://explorer.solana.com/address/${assetAddress}?cluster=devnet\n`);
      
    } catch (error) {
      console.error(`   ❌ Error checking ${assetAddress}: ${error.message}\n`);
    }
  }
}

checkNFTOwners().catch(console.error);
