// ========================================
// 🎯 CLIENT CONFIGURATION - EASY SETUP
// ========================================
// 
// Dear Client,
// 
// This is the ONLY file you need to modify!
// Just change the values below and run: npm start
//
// ========================================

module.exports = {
  // 👤 YOUR WALLET INFORMATION
  // Replace with your wallet's private key array (from your wallet export)
  WALLET_PRIVATE_KEY: [114,249,113,71,82,176,154,45,152,113,31,8,31,28,122,47,49,61,179,201,42,77,89,146,168,76,27,213,223,11,132,145,79,143,131,172,6,117,224,12,174,91,104,151,22,16,143,14,22,174,145,47,108,227,6,120,78,16,15,183,65,89,208,40],
  
  // 🎯 WHERE TO SEND THE NFTs
  // Replace with the wallet address that should receive the NFTs
  CLIENT_WALLET_ADDRESS: 'FmYrSSXT8AXfEW7HTjrNZXfWeAZ8375j3F2z6KFkseS7',
  
  // 📦 HOW MANY NFTs TO MINT
  // Change this number to mint more or fewer NFTs
  NUMBER_OF_NFTS: 25,
  
  // 🌐 SOLANA NETWORK
  // Options: 'devnet' (for testing) or 'mainnet-beta' (for real deployment)
  NETWORK: 'devnet',
  
  // 💰 BUDGET SETTINGS
  // Maximum cost you're willing to pay per NFT (in SOL)
  MAX_COST_PER_NFT: 0.009,
  
  // 🎨 NFT COLLECTION DETAILS
  // Customize your NFT collection information
  COLLECTION_NAME: 'Client Premium Collection',
  COLLECTION_SYMBOL: 'CPC',
  COLLECTION_DESCRIPTION: 'Premium NFT collection for client',
  
  // 🖼️ NFT METADATA TEMPLATE
  // This will be used for each NFT (numbers will be added automatically)
  NFT_TEMPLATE: {
    name: 'Premium NFT', // Will become "Premium NFT #1", "Premium NFT #2", etc.
    description: 'Premium NFT from client collection',
    // Simple image - you can replace this with your own image URL
    image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgZmlsbD0iIzY2N2VlYSIvPjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+UHJlbWl1bTwvdGV4dD48L3N2Zz4=",
    attributes: [
      { trait_type: "Type", value: "Premium" },
      { trait_type: "Rarity", value: "Standard" }
    ]
  },
  
  // ⚙️ ADVANCED SETTINGS (Usually don't need to change these)
  ADVANCED: {
    // RPC URL (will be set automatically based on NETWORK)
    RPC_URL: null, // Leave as null for automatic
    
    // Batch processing size
    BATCH_SIZE: 25,
    
    // Delay between mints (milliseconds) - increase if you get rate limited
    MINT_DELAY: 1500,
    
    // Number of retries if a mint fails
    RETRY_ATTEMPTS: 1,
    
    // Generate HTML report after minting
    GENERATE_REPORT: true
  }
};

// ========================================
// 📝 INSTRUCTIONS FOR DEPLOYMENT:
// ========================================
//
// 1. 🔑 GET YOUR PRIVATE KEY:
//    - Export your wallet's private key as an array
//    - Replace WALLET_PRIVATE_KEY above
//
// 2. 🎯 SET RECIPIENT ADDRESS:
//    - Replace CLIENT_WALLET_ADDRESS with where NFTs should go
//
// 3. 📦 SET NFT COUNT:
//    - Change NUMBER_OF_NFTS to how many you want
//
// 4. 🌐 CHOOSE NETWORK:
//    - 'devnet' for testing (free, fake SOL)
//    - 'mainnet-beta' for real deployment (costs real SOL)
//
// 5. 🚀 RUN THE MINTER:
//    - Open terminal/command prompt
//    - Navigate to this folder
//    - Run: npm start
//
// 6. 📊 CHECK RESULTS:
//    - HTML report will be generated automatically
//    - Check your wallet for the NFTs
//
// ========================================
// 💡 NEED HELP?
// ========================================
//
// If you get errors:
// - Make sure you have enough SOL in your wallet
// - Check that your private key is correct
// - Try 'devnet' first before 'mainnet-beta'
// - Make sure your wallet address is correct
//
// ========================================
