{"name": "enhanced-solana-nft-minter", "version": "1.0.0", "description": "Advanced Solana NFT batch minting tool with cost optimization and high performance", "main": "dist/index.js", "bin": {"solana-nft-minter": "./dist/cli.js"}, "scripts": {"build": "tsc", "start": "node client-mint.js", "test-config": "node test-config.js", "cli": "node dist/cli.js", "dev": "ts-node src/cli.ts", "test": "jest", "test:devnet": "npm run build && node dist/cli.js --network devnet --batch-size 25 --test", "clean": "rm -rf dist"}, "keywords": ["solana", "nft", "metaplex", "batch-minting", "blockchain", "crypto"], "author": "Enhanced NFT Minter", "license": "MIT", "dependencies": {"@metaplex-foundation/js": "^0.19.4", "@metaplex-foundation/mpl-core": "^1.6.0", "@metaplex-foundation/mpl-token-metadata": "^2.13.0", "@metaplex-foundation/umi": "^1.2.0", "@metaplex-foundation/umi-bundle-defaults": "^1.2.0", "@solana/spl-token": "^0.3.8", "@solana/web3.js": "^1.87.6", "chalk": "^4.1.2", "commander": "^11.1.0", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "inquirer": "^8.2.6", "ora": "^5.4.1", "p-limit": "^3.1.0", "p-retry": "^4.6.2", "winston": "^3.11.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.6", "@types/node": "^20.8.7", "jest": "^29.7.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=16.0.0"}}