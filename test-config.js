#!/usr/bin/env node

// ========================================
// 🧪 CONFIG TESTER - Test Your Setup
// ========================================
// 
// Run this to test your configuration without minting
// Command: node test-config.js
//
// ========================================

const { Keypair } = require('@solana/web3.js');
const config = require('./client-config');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function testConfig() {
  log('\n🧪 TESTING YOUR CONFIGURATION', 'blue');
  log('=====================================', 'blue');
  
  let allGood = true;
  
  // Test 1: Private Key
  log('\n🔑 Testing Private Key...', 'cyan');
  try {
    if (!config.WALLET_PRIVATE_KEY || !Array.isArray(config.WALLET_PRIVATE_KEY)) {
      log('❌ WALLET_PRIVATE_KEY must be an array', 'red');
      allGood = false;
    } else if (config.WALLET_PRIVATE_KEY.length !== 64) {
      log('❌ WALLET_PRIVATE_KEY must have exactly 64 numbers', 'red');
      allGood = false;
    } else {
      const wallet = Keypair.fromSecretKey(new Uint8Array(config.WALLET_PRIVATE_KEY));
      log(`✅ Private key valid`, 'green');
      log(`   Wallet address: ${wallet.publicKey.toBase58()}`, 'white');
    }
  } catch (error) {
    log(`❌ Invalid private key: ${error.message}`, 'red');
    allGood = false;
  }
  
  // Test 2: Client Address
  log('\n🎯 Testing Client Address...', 'cyan');
  try {
    if (!config.CLIENT_WALLET_ADDRESS || typeof config.CLIENT_WALLET_ADDRESS !== 'string') {
      log('❌ CLIENT_WALLET_ADDRESS must be a string', 'red');
      allGood = false;
    } else if (config.CLIENT_WALLET_ADDRESS.length < 32 || config.CLIENT_WALLET_ADDRESS.length > 44) {
      log('❌ CLIENT_WALLET_ADDRESS doesn\'t look like a valid Solana address', 'red');
      allGood = false;
    } else {
      log(`✅ Client address looks valid`, 'green');
      log(`   Address: ${config.CLIENT_WALLET_ADDRESS}`, 'white');
    }
  } catch (error) {
    log(`❌ Invalid client address: ${error.message}`, 'red');
    allGood = false;
  }
  
  // Test 3: NFT Count
  log('\n📦 Testing NFT Count...', 'cyan');
  if (!config.NUMBER_OF_NFTS || typeof config.NUMBER_OF_NFTS !== 'number' || config.NUMBER_OF_NFTS < 1) {
    log('❌ NUMBER_OF_NFTS must be a number greater than 0', 'red');
    allGood = false;
  } else if (config.NUMBER_OF_NFTS > 1000) {
    log('⚠️  NUMBER_OF_NFTS is very high, consider starting smaller', 'yellow');
    log(`✅ NFT count: ${config.NUMBER_OF_NFTS}`, 'green');
  } else {
    log(`✅ NFT count: ${config.NUMBER_OF_NFTS}`, 'green');
  }
  
  // Test 4: Network
  log('\n🌐 Testing Network...', 'cyan');
  if (!config.NETWORK || !['devnet', 'mainnet-beta'].includes(config.NETWORK)) {
    log('❌ NETWORK must be either "devnet" or "mainnet-beta"', 'red');
    allGood = false;
  } else {
    log(`✅ Network: ${config.NETWORK}`, 'green');
    if (config.NETWORK === 'devnet') {
      log('   💡 Good choice for testing!', 'cyan');
    } else {
      log('   ⚠️  This will use real SOL!', 'yellow');
    }
  }
  
  // Test 5: Budget
  log('\n💰 Testing Budget...', 'cyan');
  if (!config.MAX_COST_PER_NFT || typeof config.MAX_COST_PER_NFT !== 'number' || config.MAX_COST_PER_NFT <= 0) {
    log('❌ MAX_COST_PER_NFT must be a positive number', 'red');
    allGood = false;
  } else {
    log(`✅ Max cost per NFT: ${config.MAX_COST_PER_NFT} SOL (budget limit)`, 'green');
    const budgetTotal = config.MAX_COST_PER_NFT * config.NUMBER_OF_NFTS;
    const actualTotal = 0.003 * config.NUMBER_OF_NFTS; // Core NFT actual cost
    log(`   Budget total: ${budgetTotal.toFixed(6)} SOL (worst case)`, 'yellow');
    log(`   Actual cost: ~${actualTotal.toFixed(6)} SOL (Core NFTs)`, 'green');
    log(`   You'll save: ~${(budgetTotal - actualTotal).toFixed(6)} SOL!`, 'cyan');
  }
  
  // Test 6: Collection Info
  log('\n🎨 Testing Collection Info...', 'cyan');
  if (!config.COLLECTION_NAME || typeof config.COLLECTION_NAME !== 'string') {
    log('⚠️  COLLECTION_NAME should be a string', 'yellow');
  } else {
    log(`✅ Collection name: ${config.COLLECTION_NAME}`, 'green');
  }
  
  if (!config.NFT_TEMPLATE || typeof config.NFT_TEMPLATE !== 'object') {
    log('⚠️  NFT_TEMPLATE should be an object', 'yellow');
  } else {
    log(`✅ NFT template configured`, 'green');
    log(`   Name template: ${config.NFT_TEMPLATE.name}`, 'white');
  }
  
  // Final Result
  log('\n=====================================', allGood ? 'green' : 'red');
  if (allGood) {
    log('🎉 ALL TESTS PASSED!', 'green');
    log('Your configuration looks perfect!', 'green');
    log('\nNext steps:', 'cyan');
    log('1. Run: npm start', 'white');
    log('2. Wait for minting to complete', 'white');
    log('3. Check your wallet for NFTs', 'white');
  } else {
    log('❌ SOME TESTS FAILED!', 'red');
    log('Please fix the issues above in client-config.js', 'red');
    log('\nThen run this test again:', 'yellow');
    log('node test-config.js', 'white');
  }
  log('=====================================', allGood ? 'green' : 'red');
  
  return allGood;
}

// Run the test
testConfig();
