const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { createUmi } = require('@metaplex-foundation/umi-bundle-defaults');
const { keypairIdentity, publicKey } = require('@metaplex-foundation/umi');
const { generateSigner, percentAmount } = require('@metaplex-foundation/umi');
const { create, mplCore } = require('@metaplex-foundation/mpl-core');
const fs = require('fs-extra');

async function mintCoreNFTsForClient() {
  console.log('🚀 Minting NEW Core NFTs directly to Client Wallet\n');
  
  // Load wallet
  const envContent = await fs.readFile('.env', 'utf8');
  const privateKeyMatch = envContent.match(/PRIVATE_KEY=(.+)/);
  if (!privateKeyMatch) {
    throw new Error('PRIVATE_KEY not found in .env file');
  }
  const privateKeyArray = JSON.parse(privateKeyMatch[1]);
  const wallet = Keypair.fromSecretKey(new Uint8Array(privateKeyArray));
  
  // Setup UMI
  const umi = createUmi('https://api.devnet.solana.com');
  umi.use(mplCore());
  umi.use(keypairIdentity({
    publicKey: publicKey(wallet.publicKey.toBase58()),
    secretKey: wallet.secretKey
  }));

  // Client address
  const clientAddress = 'FmYrSSXT8AXfEW7HTjrNZXfWeAZ8375j3F2z6KFkseS7';
  console.log(`🎯 Client Address: ${clientAddress}`);
  console.log(`💰 Payer Address: ${wallet.publicKey.toBase58()}\n`);

  // Minting configuration
  const totalNFTs = 25;
  const results = [];
  let successCount = 0;
  let failCount = 0;
  let totalCost = 0;
  const startTime = Date.now();

  console.log(`📦 Minting ${totalNFTs} Core NFTs directly to client wallet...\n`);

  for (let i = 1; i <= totalNFTs; i++) {
    try {
      console.log(`⏳ Minting NFT ${i}/${totalNFTs}...`);
      
      const nftStartTime = Date.now();
      
      // Get initial balance
      const connection = new Connection('https://api.devnet.solana.com');
      const initialBalance = await connection.getBalance(wallet.publicKey);
      
      // Generate asset signer
      const asset = generateSigner(umi);
      
      // Create minimal metadata URI
      const metadata = {
        name: `Client Core NFT #${i}`,
        description: `Premium Core NFT #${i} for client - meets budget requirements`,
        image: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNTEyIiBoZWlnaHQ9IjUxMiIgZmlsbD0iIzY2N2VlYSIvPjx0ZXh0IHg9IjI1NiIgeT0iMjU2IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Q29yZSBORlQ8L3RleHQ+PC9zdmc+"
      };
      
      const metadataUri = `data:application/json;base64,${Buffer.from(JSON.stringify(metadata)).toString('base64')}`;
      
      // Create Core NFT directly with client as owner
      const createTx = await create(umi, {
        asset,
        name: metadata.name,
        uri: metadataUri,
        owner: publicKey(clientAddress), // Direct ownership to client
        plugins: []
      });

      const signature = await createTx.sendAndConfirm(umi);
      
      // Get final balance and calculate cost
      const finalBalance = await connection.getBalance(wallet.publicKey);
      const cost = (initialBalance - finalBalance) / 1e9; // Convert to SOL
      const timeTaken = (Date.now() - nftStartTime) / 1000;
      
      console.log(`✅ NFT ${i} minted successfully!`);
      console.log(`   Asset Address: ${asset.publicKey}`);
      console.log(`   Owner: ${clientAddress}`);
      console.log(`   Cost: ${cost.toFixed(6)} SOL`);
      console.log(`   Time: ${timeTaken.toFixed(2)}s`);
      console.log(`   Explorer: https://explorer.solana.com/address/${asset.publicKey}?cluster=devnet\n`);

      results.push({
        success: true,
        nftNumber: i,
        assetAddress: asset.publicKey.toString(),
        signature: signature,
        cost: cost,
        timeTaken: timeTaken,
        metadata: metadata,
        owner: clientAddress,
        explorerUrl: `https://explorer.solana.com/address/${asset.publicKey}?cluster=devnet`
      });

      successCount++;
      totalCost += cost;

      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1500));

    } catch (error) {
      console.error(`❌ Failed to mint NFT ${i}`);
      console.error(`   Error: ${error.message}\n`);
      
      results.push({
        success: false,
        nftNumber: i,
        error: error.message
      });

      failCount++;
    }
  }

  const totalTime = (Date.now() - startTime) / 1000;
  const avgCost = successCount > 0 ? totalCost / successCount : 0;
  const nftsPerHour = totalTime > 0 ? Math.round((successCount / totalTime) * 3600) : 0;

  const finalResults = {
    clientAddress: clientAddress,
    successfulMints: successCount,
    failedMints: failCount,
    totalCost: totalCost,
    averageCostPerNFT: avgCost,
    totalTime: totalTime,
    nftsPerHour: nftsPerHour,
    results: results,
    timestamp: Date.now(),
    nftType: 'Metaplex Core NFT',
    budgetTarget: 0.009,
    budgetMet: avgCost <= 0.009
  };

  // Save results
  const resultsFile = `client-core-nfts-${Date.now()}.json`;
  await fs.writeJson(resultsFile, finalResults, { spaces: 2 });

  console.log('\n🎉 Core NFT Minting Complete!');
  console.log(`📊 Final Results:`);
  console.log(`   ✅ Successful mints: ${successCount}/${totalNFTs}`);
  console.log(`   ❌ Failed mints: ${failCount}/${totalNFTs}`);
  console.log(`   💰 Average cost: ${avgCost.toFixed(6)} SOL per NFT`);
  console.log(`   🎯 Budget target: ≤0.009 SOL per NFT`);
  console.log(`   ${avgCost <= 0.009 ? '✅' : '❌'} Budget ${avgCost <= 0.009 ? 'MET' : 'EXCEEDED'}`);
  console.log(`   💸 Total cost: ${totalCost.toFixed(6)} SOL`);
  console.log(`   ⚡ Performance: ${nftsPerHour} NFTs/hour`);
  console.log(`   ⏱️  Total time: ${(totalTime / 60).toFixed(1)} minutes`);
  console.log(`   👤 Owner: ${clientAddress}`);
  console.log(`   📄 Results saved: ${resultsFile}\n`);

  if (successCount > 0) {
    console.log('🔗 Sample minted NFT addresses (all owned by client):');
    results.filter(r => r.success).slice(0, 5).forEach(nft => {
      console.log(`   ${nft.assetAddress} - ${nft.explorerUrl}`);
    });
    console.log('\n💡 All NFTs are now directly owned by the client wallet!');
  }

  return { finalResults, resultsFile };
}

// Run if called directly
if (require.main === module) {
  mintCoreNFTsForClient()
    .then(({ finalResults, resultsFile }) => {
      console.log(`\n🎯 SUCCESS: ${finalResults.successfulMints} Core NFTs minted directly to client wallet!`);
      console.log(`📄 Results file: ${resultsFile}`);
    })
    .catch(console.error);
}

module.exports = { mintCoreNFTsForClient };
