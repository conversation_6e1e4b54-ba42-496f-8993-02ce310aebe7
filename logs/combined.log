{"error":"failed to get balance of account 78nPcoyXnBUpLmDGGtguKEX7vZoR3ZfPmrsHMsfVSPu3: TypeError: fetch failed","level":"error","message":"Wallet validation failed","timestamp":"2025-07-31T08:25:36.443Z"}
{"error":"failed to get balance of account 6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq: Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"invalid api key provided\"}}","level":"error","message":"Wallet validation failed","timestamp":"2025-07-31T08:33:59.109Z"}
{"balance":"2.000000 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T08:34:40.415Z"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T08:35:03.709Z","totalNFTs":25}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T08:35:03.716Z","totalBatches":1}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #3","mintAddress":"EuSs1Uc8uibrWXKmUHAEiyhpN8xwmctWwcmRfYHKnaUe","timeTaken":"12.52s","timestamp":"2025-07-31T08:36:07.096Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #2","mintAddress":"FFh7wUMhTxw79HknFP2zdaiFc7zbi3URL76BczEpHFxp","timeTaken":"12.55s","timestamp":"2025-07-31T08:36:07.112Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #1","mintAddress":"9Mn5SLrcJpxPCc2gyTw4N6cRwvnwU398euZnpgoRxu4u","timeTaken":"12.58s","timestamp":"2025-07-31T08:36:07.127Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #6","mintAddress":"3nfk99AMFdjvBRWUgQVjRUWa1ctVPUtSuqb8Qjq7nQSR","timeTaken":"6.42s","timestamp":"2025-07-31T08:36:13.832Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #5","mintAddress":"GWhL9ajxSwdnoTVQG35fjoEC7GDWDsseaMjutAougqo8","timeTaken":"7.17s","timestamp":"2025-07-31T08:36:14.567Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #4","mintAddress":"49pT6pkfvCkztmCHqTub6T48JqiBknvNzPSCL2dJ2pPP","timeTaken":"9.03s","timestamp":"2025-07-31T08:36:16.466Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #8","mintAddress":"BG9eSUWU73t1EcJZgWqb5Pe9gzBTbBP6jbRQG1wYFEVv","timeTaken":"9.30s","timestamp":"2025-07-31T08:36:24.156Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #7","mintAddress":"6chBnVLvZLv9bxxxvNoLCwAeaupuy4CLnypECttyNKPY","timeTaken":"10.48s","timestamp":"2025-07-31T08:36:24.599Z"}
{"cost":"0.078626 SOL","level":"info","message":"✅ Successfully minted: Test NFT #9","mintAddress":"7kLEFUZMo6QX3BABfYaPW7PL7tR7cmU8iwgYsRhHPbam","timeTaken":"14.60s","timestamp":"2025-07-31T08:36:31.339Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #10","mintAddress":"7Q76PtYW21UrqBkAnRk27XHGeCK3HbdM4Zk11RqntyDC","timeTaken":"18.17s","timestamp":"2025-07-31T08:36:43.128Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #12","mintAddress":"AUbgnL1AiEUmmGx7zuzqsLVipQtp75HA6qWf3856sH8U","timeTaken":"11.02s","timestamp":"2025-07-31T08:36:43.538Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #11","mintAddress":"D2TGVFq9kyCh2DfeddzHtNn2BpkQmgFFH1Kej67Ys5s7","timeTaken":"17.81s","timestamp":"2025-07-31T08:36:43.555Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #13","mintAddress":"2N1nhhSPxjWQy9eWVXHMNQiC3G1Ezva3uV3KTnzYXuQb","timeTaken":"9.12s","timestamp":"2025-07-31T08:36:53.151Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #15","mintAddress":"ADcJdX9zcDPCZc6EjJHnBdrxTL9PthiQxMHPBwt4PxZS","timeTaken":"8.70s","timestamp":"2025-07-31T08:36:53.461Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #14","mintAddress":"G9rZLfGxKDMCbYxUG5ivTY22cjroPaueMYXSY6rpvGcs","timeTaken":"8.70s","timestamp":"2025-07-31T08:36:53.477Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #16","mintAddress":"B1ggYLnxXAZurfHzbUm9edySahxe9DDDmmHG66Z112oG","timeTaken":"5.18s","timestamp":"2025-07-31T08:36:58.772Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #18","mintAddress":"FMJ1dSmxEmv99FDhEDdLrCm3QbZeuitXT8fGbBWGMLbs","timeTaken":"5.82s","timestamp":"2025-07-31T08:36:59.576Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #17","mintAddress":"4JxNrDUBdJPbfJnvXQV8WZbRYvJLDg9v8FT23PHhe3UD","timeTaken":"5.84s","timestamp":"2025-07-31T08:36:59.754Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #21","mintAddress":"FiDnUbrPS5e8ffQCTB7JsnbQ7pUkB7aN1XTAgp8mXJWm","timeTaken":"4.41s","timestamp":"2025-07-31T08:37:04.556Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #19","mintAddress":"5JZgWP1Csk2pyq6SJSVtNboBf1ephWDfj1S1nsRp379Y","timeTaken":"5.76s","timestamp":"2025-07-31T08:37:05.028Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #20","mintAddress":"GmU31GiyahT2JUmCzxubqjuQVzj5T4wJX4eRb7U35PMJ","timeTaken":"4.80s","timestamp":"2025-07-31T08:37:05.320Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #24","mintAddress":"G48qpCAEH32iXLu94XVNipd6tMiivoUnAmoKSpUBmK4q","timeTaken":"10.19s","timestamp":"2025-07-31T08:37:15.786Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #22","mintAddress":"2oQeGiKEk6Uuji6DYEpXfoWJBRYiMJ8ABbraHwMLBZvS","timeTaken":"10.97s","timestamp":"2025-07-31T08:37:15.818Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #23","mintAddress":"7Kf3rnBsXDdPDNKEzDTLRZ9Ft1e4cSvfma8vKzk2XHFk","timeTaken":"10.47s","timestamp":"2025-07-31T08:37:15.911Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #25","mintAddress":"Bd96vEM9Dh97qPLh1yvW4zqELUNBmxw4iWH42u1a43BQ","timeTaken":"6.83s","timestamp":"2025-07-31T08:37:23.142Z"}
{"cost":"1.415272 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":25,"timeTaken":"139.43s","timestamp":"2025-07-31T08:37:23.145Z"}
{"avgCostPerNFT":"0.056611 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":645,"successRate":"100.0%","timestamp":"2025-07-31T08:37:23.148Z","totalCost":"1.415272 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"139.44s"}
{"balance":"1.508495 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T08:43:45.352Z"}
{"balance":"1.508495 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T08:44:24.799Z"}
{"batchSize":5,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T08:46:22.655Z","totalNFTs":5}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":5,"progress":"0.0%","timestamp":"2025-07-31T08:46:22.666Z","totalBatches":1}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #1","mintAddress":"HaoecjiULSvsrtmXEnp1b3V6eZrheB7phNNnqgYUaduM","timeTaken":"28.09s","timestamp":"2025-07-31T08:47:05.834Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #2","mintAddress":"DQvieyJVVtP6SxSPWzEzgckHKXxdaL74jSZed29g8u9P","timeTaken":"28.51s","timestamp":"2025-07-31T08:47:06.227Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #3","mintAddress":"BibGAXsr3tfnUuaPinHEddRQH6ePQ8YV1aF1kPM1u1oy","timeTaken":"28.53s","timestamp":"2025-07-31T08:47:06.245Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #4","mintAddress":"8ESvq4rDddZKxLKyMyhCJGmVbeNmx2UaPkJrwdJ9npJP","timeTaken":"5.95s","timestamp":"2025-07-31T08:47:12.269Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #5","mintAddress":"3jZfpXdLH5JiGxARyUF2nMD5hYVFsJNfb75D915QBkF7","timeTaken":"5.58s","timestamp":"2025-07-31T08:47:12.283Z"}
{"cost":"0.255535 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":5,"timeTaken":"49.62s","timestamp":"2025-07-31T08:47:12.288Z"}
{"avgCostPerNFT":"0.051107 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":363,"successRate":"100.0%","timestamp":"2025-07-31T08:47:12.294Z","totalCost":"0.255535 SOL","totalFailed":0,"totalSuccessful":5,"totalTime":"49.64s"}
{"error":"failed to get balance of account 6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq: TypeError: fetch failed","level":"error","message":"Wallet validation failed","timestamp":"2025-07-31T09:28:33.068Z"}
{"error":"failed to get balance of account 6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq: Error: 401 Unauthorized: {\"jsonrpc\":\"2.0\",\"error\":{\"code\":-32401,\"message\":\"missing api key\"}}","level":"error","message":"Wallet validation failed","timestamp":"2025-07-31T09:29:07.791Z"}
{"balance":"1.410212 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T09:29:37.411Z"}
{"balance":"1.410212 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T09:30:41.317Z"}
{"balance":"1.410212 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T09:31:16.820Z"}
{"batchSize":5,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T09:31:27.222Z","totalNFTs":5}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":5,"progress":"0.0%","timestamp":"2025-07-31T09:31:27.224Z","totalBatches":1}
{"level":"warn","message":"Mint attempt failed for Test NFT #1: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program log: Please upgrade to SPL Token 2022 for immutable owner support\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 3993 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 111 of 111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 19030 of 19030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 49607 of 49607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 66381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 59911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 100093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 10633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 3993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 111 of 111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 19030 of 19030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 49607 of 49607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:31:47.317Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #2: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 14493 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 10611 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 26530 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 69381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 62911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 97093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 21133 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 14493 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 10611 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 26530 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\n","timestamp":"2025-07-31T09:31:47.350Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #3: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:31:47.615Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #3: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:31:50.203Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #2: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:31:50.524Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #1: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 12993 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 9111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 25030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 69381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 62911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 97093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 19633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 12993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 9111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 25030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\n","timestamp":"2025-07-31T09:31:51.101Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #3: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Computational budget exceeded. \nLogs: \n[\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 107593 of 149700 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\",\n  \"Program log: IX: Mint\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\",\n  \"Program log: Create\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 5530 of 5530 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Computational budget exceeded\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 42107 of 42107 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 58881 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 52411 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 107593 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 5530 of 5530 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Computational budget exceeded\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 42107 of 42107 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\n","timestamp":"2025-07-31T09:31:53.527Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #1: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:31:54.466Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #2: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 9993 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 6111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 23443 of 25030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 54107 of 54107 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 70881 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 64411 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 95593 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 16633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 9993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 6111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 23443 of 25030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 54107 of 54107 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\n","timestamp":"2025-07-31T09:31:55.835Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #3: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 14493 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 10611 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 26530 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 69381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 62911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 97093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 21133 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 14493 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 10611 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 26530 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\n","timestamp":"2025-07-31T09:31:59.956Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","level":"error","message":"Failed to mint NFT: Test NFT #3","timestamp":"2025-07-31T09:31:59.959Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #1: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Computational budget exceeded. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 15984 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 12102 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 28021 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 54107 of 54107 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 70881 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 64411 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 95593 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 22624 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 15984 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 12102 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 28021 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 54107 of 54107 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\n","timestamp":"2025-07-31T09:32:00.623Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Computational budget exceeded. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 15984 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 12102 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 28021 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 54107 of 54107 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 70881 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 64411 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 95593 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 22624 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 15984 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 12102 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 28021 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 54107 of 54107 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\n","level":"error","message":"Failed to mint NFT: Test NFT #1","timestamp":"2025-07-31T09:32:00.627Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #4: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:32:01.608Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #2: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Computational budget exceeded. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 11493 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 7611 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 21943 of 25030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 69381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 62911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 97093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 18133 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 11493 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 7611 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 21943 of 25030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\n","timestamp":"2025-07-31T09:32:02.042Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Computational budget exceeded. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 11493 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 7611 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 21943 of 25030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 69381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 62911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 97093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 18133 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 11493 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 7611 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 21943 of 25030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Computational budget exceeded\n","level":"error","message":"Failed to mint NFT: Test NFT #2","timestamp":"2025-07-31T09:32:02.044Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #5: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 12993 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 9111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 23443 of 28030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 19633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 12993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 9111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 23443 of 28030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\n","timestamp":"2025-07-31T09:32:02.416Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #4: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 8493 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 4611 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 23443 of 23530 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 69381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 62911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 97093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 15133 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 8493 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 4611 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 23443 of 23530 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 52607 of 52607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: exceeded CUs meter at BPF instruction\n","timestamp":"2025-07-31T09:32:03.843Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #5: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program log: Instruction: GetAccountDataSize\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 1633 compute units\",\n  \"Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 8530 of 8530 compute units\",\n  \"Program return: ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL pQAAAAAAAAA=\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 43607 of 43607 compute units\",\n  \"Program return: metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s pQAAAAAAAAA=\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 60381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 53911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 106093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 1633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 8530 of 8530 compute units\n| Program return: ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL pQAAAAAAAAA=\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 43607 of 43607 compute units\n| Program return: metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s pQAAAAAAAAA=\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:32:04.703Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #4: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:32:07.111Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #5: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:32:07.991Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #4: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 6993 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program log: Instruction: InitializeAccount3\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3111 of 3111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 25030 of 25030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 13633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 6993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3111 of 3111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 25030 of 25030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:32:12.444Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\",\n  \"Program log: Instruction: MintTo\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 72381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 65911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 94093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: GetAccountDataSize\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1622 of 25633 compute units\n| Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ******************************** invoke [3]\n| Program ******************************** success\n| Program log: Initialize the associated token account\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeImmutableOwner\n| Program log: Please upgrade to SPL Token 2022 for immutable owner support\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 18993 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program log: Instruction: InitializeAccount3\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4241 of 15111 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20443 of 31030 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: MintTo\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2832 of 2832 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 55607 of 55607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","level":"error","message":"Failed to mint NFT: Test NFT #4","timestamp":"2025-07-31T09:32:12.449Z"}
{"level":"warn","message":"Mint attempt failed for Test NFT #5: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program log: IX: Mint\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\",\n  \"Program log: Create\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 133 of 133 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 8530 of 8530 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 43607 of 43607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 60381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 53911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 106093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 133 of 133 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 8530 of 8530 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 43607 of 43607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","timestamp":"2025-07-31T09:32:14.347Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error that is not recognized by the programs registered on the SDK. Please check the underlying program error below for more details.\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: Error: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 3: Program failed to complete. \nLogs: \n[\n  \"Program log: IX: Mint\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\",\n  \"Program log: Create\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 133 of 133 compute units\",\n  \"Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 8530 of 8530 compute units\",\n  \"Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 43607 of 43607 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.\n\nProgram Logs:\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program ComputeBudget************************111111 invoke [1]\n| Program ComputeBudget************************111111 success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 130002 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Transfer 1030080 lamports to the new account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3090 of 60381 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: SetAuthority\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3250 of 53911 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 106093 of 149700 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s success\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Mint\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]\n| Program log: Create\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 133 of 133 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA failed: exceeded CUs meter at BPF instruction\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 8530 of 8530 compute units\n| Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL failed: Program failed to complete\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 43607 of 43607 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: Program failed to complete\n","level":"error","message":"Failed to mint NFT: Test NFT #5","timestamp":"2025-07-31T09:32:14.349Z"}
{"cost":"0.000000 SOL","failed":5,"level":"info","message":"✅ Batch 1 completed","successRate":"0.0%","successful":0,"timeTaken":"47.13s","timestamp":"2025-07-31T09:32:14.351Z"}
{"avgCostPerNFT":"0.000000 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":0,"successRate":"0.0%","timestamp":"2025-07-31T09:32:14.353Z","totalCost":"0.000000 SOL","totalFailed":5,"totalSuccessful":0,"totalTime":"47.13s"}
{"balance":"1.410212 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T09:35:16.034Z"}
{"batchSize":5,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T09:35:29.876Z","totalNFTs":5}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":5,"progress":"0.0%","timestamp":"2025-07-31T09:35:29.879Z","totalBatches":1}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #3","mintAddress":"8993VYRsRTijN6GhhH2hrHPg5Cjnb7ECrxUU1SjZn6nY","timeTaken":"3.88s","timestamp":"2025-07-31T09:35:43.740Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #2","mintAddress":"********************************************","timeTaken":"3.89s","timestamp":"2025-07-31T09:35:44.211Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #1","mintAddress":"*******************************************J","timeTaken":"6.46s","timestamp":"2025-07-31T09:35:46.222Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #4","mintAddress":"EWMYAfGLi3KQbq95vBJtq39E52nNDCknB6WfJ4uEuomn","timeTaken":"2.50s","timestamp":"2025-07-31T09:35:46.617Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #5","mintAddress":"3DygCWNLK6nL1NmEo6T22jramrMhmQfu5VmRkHXN6pES","timeTaken":"2.02s","timestamp":"2025-07-31T09:35:46.631Z"}
{"cost":"0.255535 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":5,"timeTaken":"16.75s","timestamp":"2025-07-31T09:35:46.635Z"}
{"avgCostPerNFT":"0.051107 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":1074,"successRate":"100.0%","timestamp":"2025-07-31T09:35:46.637Z","totalCost":"0.255535 SOL","totalFailed":0,"totalSuccessful":5,"totalTime":"16.76s"}
{"balance":"1.311929 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T09:42:37.812Z"}
{"batchSize":5,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T09:42:43.094Z","totalNFTs":5}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":5,"progress":"0.0%","timestamp":"2025-07-31T09:42:43.099Z","totalBatches":1}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #3","mintAddress":"DeHBYGSKQ3jD2RFHPNnPkWAZZyW9vVk4K773e8drsjEt","timeTaken":"4.76s","timestamp":"2025-07-31T09:43:05.640Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #1","mintAddress":"EW1bAQfXVdo8TctS8V2UeusxhoGdhZzfhpojPxw5wCS1","timeTaken":"4.74s","timestamp":"2025-07-31T09:43:05.644Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #2","mintAddress":"EaiANhcdLp15sC1M7NS9LVibBDVEsnA9XFCTZkn6KKbw","timeTaken":"4.75s","timestamp":"2025-07-31T09:43:05.990Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #5","mintAddress":"HxhNmHnDBqxU4wWkfJN5Uby7AbSZNXXc9RG3pdwfNV12","timeTaken":"6.11s","timestamp":"2025-07-31T09:43:12.574Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #4","mintAddress":"2gVwaByu2Szgo8xTU4SGCtTEHBPafHHh6qAj1QtXSLhL","timeTaken":"6.13s","timestamp":"2025-07-31T09:43:12.601Z"}
{"cost":"0.255535 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":5,"timeTaken":"29.50s","timestamp":"2025-07-31T09:43:12.611Z"}
{"avgCostPerNFT":"0.051107 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":610,"successRate":"100.0%","timestamp":"2025-07-31T09:43:12.617Z","totalCost":"0.255535 SOL","totalFailed":0,"totalSuccessful":5,"totalTime":"29.52s"}
{"balance":"1.213646 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T09:50:14.529Z"}
{"balance":"1.213646 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T09:54:35.622Z"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T09:56:14.902Z","totalNFTs":25}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T09:56:14.908Z","totalBatches":1}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #2","mintAddress":"6MoVpQBNkV24UNwafyEX7GUWeTnX28wASnEDf6rDd3KX","timeTaken":"6.20s","timestamp":"2025-07-31T09:57:00.676Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #1","mintAddress":"8bGUWEUrogfJK9KfeaSCJsVbmpPS9QuDpzVPSPvPqCMi","timeTaken":"6.50s","timestamp":"2025-07-31T09:57:00.690Z"}
{"cost":"0.098283 SOL","level":"info","message":"✅ Successfully minted: Test NFT #3","mintAddress":"4JUow9jYtBoSV19AYfvxmWBbodehLRGC6pq6L4eNY9ri","timeTaken":"9.21s","timestamp":"2025-07-31T09:57:03.410Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #4","mintAddress":"CRUewX6Py2AoHELuh5HM8wwTeqNAu6e3azewxGyEYqTk","timeTaken":"8.08s","timestamp":"2025-07-31T09:57:09.029Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #5","mintAddress":"62sQMHYS6GLxSaBp1ftrbi543F6UbXRTJzcb5PhEk4Er","timeTaken":"8.71s","timestamp":"2025-07-31T09:57:09.693Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #6","mintAddress":"AwBcXcEwc2vn91kJGdrmA3Wq4zcYa1BrnAo3HYpVuRdD","timeTaken":"14.36s","timestamp":"2025-07-31T09:57:18.029Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #8","mintAddress":"J2XkEgMBhbeYjaGwsbtKxSQ8tay77sorWRWMBhnCcZ1a","timeTaken":"8.08s","timestamp":"2025-07-31T09:57:18.077Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #7","mintAddress":"4ocEYHvD6TWLJnfyT1Zmjm28mFCyqeGQc7PFCVFrbBha","timeTaken":"8.74s","timestamp":"2025-07-31T09:57:18.181Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #11","mintAddress":"36WL4H1NWm4C7BsUCL4EAW1DgESHHGvE5QXRFkA98FhD","timeTaken":"9.33s","timestamp":"2025-07-31T09:57:27.935Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #9","mintAddress":"GY5M4mf1r2CNuieNgih4oD6WYvXJ1fsiSfabybMVqWtd","timeTaken":"9.50s","timestamp":"2025-07-31T09:57:28.743Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #10","mintAddress":"Hq69sU4bWudScm2T7qF2cWi8yLhhr9z7cGn8gSys11qc","timeTaken":"10.34s","timestamp":"2025-07-31T09:57:29.542Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #12","mintAddress":"7VusJgCiNo8QzTW8uH5BctRcgzmRETbcoEQWLQcdy7hQ","timeTaken":"5.51s","timestamp":"2025-07-31T09:57:33.833Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #14","mintAddress":"EXsekUjP8nCzjYyYDJf9gxTFCNHCGPoRpnVmXyHDBmjf","timeTaken":"4.45s","timestamp":"2025-07-31T09:57:34.402Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #13","mintAddress":"142CPt3CgwfPG6V7EWGo3kptCoUPZTMw4EMi3vN7GhaT","timeTaken":"5.55s","timestamp":"2025-07-31T09:57:34.676Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #17","mintAddress":"CfC4R3CRmGwFLgP7DjdaaBHk2vR8DdqAPZJd8GTqjGU1","timeTaken":"4.36s","timestamp":"2025-07-31T09:57:39.416Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #16","mintAddress":"AVDbqKDpVjmFyAo46pKxcPVhMFAUcGQDpCYpqp4V5Vkw","timeTaken":"5.63s","timestamp":"2025-07-31T09:57:40.463Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #15","mintAddress":"9JVrcm2KoGrMrUZg2th8gULr8oNpAtSxJEGTXDWCXi7J","timeTaken":"7.59s","timestamp":"2025-07-31T09:57:41.808Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #20","mintAddress":"JBwxdeY14L9VnjMpXLP1pv9K1WHR2s8DMRG5xioQG9JF","timeTaken":"11.63s","timestamp":"2025-07-31T09:57:53.853Z"}
{"cost":"0.078626 SOL","level":"info","message":"✅ Successfully minted: Test NFT #18","mintAddress":"EQr9Hjh3eyH2qkJVHBGiAMdi6oyHooQZ6rvhZfdvULyf","timeTaken":"23.50s","timestamp":"2025-07-31T09:58:03.390Z"}
{"cost":"0.078626 SOL","level":"info","message":"✅ Successfully minted: Test NFT #19","mintAddress":"51J2a6FayHjnPUK7MZpiaau1bvNr1dtCtsz3q4SmH4gi","timeTaken":"22.47s","timestamp":"2025-07-31T09:58:04.002Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #21","mintAddress":"8r9ymoWzjoGcEjt1ktDw8q1LWHwxTNeoeyPj1ugrbefH","timeTaken":"11.96s","timestamp":"2025-07-31T09:58:06.210Z"}
{"cost":"0.039313 SOL","level":"info","message":"✅ Successfully minted: Test NFT #24","mintAddress":"2Ykin3sH9ocVyDzVaqDwkNx8ns3wZf9GHxEauJ59R6Hp","timeTaken":"3.71s","timestamp":"2025-07-31T09:58:10.207Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #22","mintAddress":"42wNKoWveDrNPJkq6rBArVmN1ameAHhojKV9ZKitfaWL","timeTaken":"6.52s","timestamp":"2025-07-31T09:58:10.320Z"}
{"cost":"0.058970 SOL","level":"info","message":"✅ Successfully minted: Test NFT #23","mintAddress":"cAj7FGoFtRc94uXp7iDWCQWEnwjcFmpfdf2TC4opN1G","timeTaken":"5.96s","timestamp":"2025-07-31T09:58:10.348Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #25","mintAddress":"E798FFTSe2jF4VEsmUpdj7iGNJjJtrfigtQ8S3Xq8ZqP","timeTaken":"6.35s","timestamp":"2025-07-31T09:58:16.962Z"}
{"cost":"1.434929 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":25,"timeTaken":"122.05s","timestamp":"2025-07-31T09:58:16.966Z"}
{"avgCostPerNFT":"0.057397 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":737,"successRate":"100.0%","timestamp":"2025-07-31T09:58:16.970Z","totalCost":"1.434929 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"122.07s"}
{"balance":"0.722232 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T10:03:33.939Z"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T10:05:15.105Z","totalNFTs":25}
{"level":"info","message":"🚀 Processing all NFTs in parallel for maximum speed","timestamp":"2025-07-31T10:05:15.107Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #4","mintAddress":"9P9R13C2WzxJ7BQhXMokkUv5DdgKiv37KTqGj3cAieAq","timeTaken":"5.72s","timestamp":"2025-07-31T10:05:41.365Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #1","mintAddress":"D5ENGnLPZTiKxd1RFxkBik7iW9vYaN7RaYsshR1QvV2T","timeTaken":"5.74s","timestamp":"2025-07-31T10:05:41.370Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #6","mintAddress":"EUbUA5DSMWugCUANJQEWMoDg5w9mmVvhpQoWPvDTAwB9","timeTaken":"5.75s","timestamp":"2025-07-31T10:05:41.381Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #2","mintAddress":"3LFeaDFenmkFfWPjo6dTDVmCwoQNJgFqpT2KxtrMTepP","timeTaken":"5.74s","timestamp":"2025-07-31T10:05:41.508Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #9","mintAddress":"Aj2GLifR84Qpm3KJFLR9zPyo2ngiwRpQV5efgrD3b9Pd","timeTaken":"6.18s","timestamp":"2025-07-31T10:05:41.830Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #10","mintAddress":"GiEM82WTL71rTG4dBHVRfMcdcuY37XgmJEWT7gcmvmFX","timeTaken":"6.08s","timestamp":"2025-07-31T10:05:41.836Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #5","mintAddress":"HW73zJjCxXZSkY1wLoQok72iYK3WkcTcA22jeiDFf7wz","timeTaken":"6.19s","timestamp":"2025-07-31T10:05:41.959Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #3","mintAddress":"3HrnAUDDsrSHPMtib6Ez6kXJwL9rQPNQdz3KysB3TJRf","timeTaken":"6.33s","timestamp":"2025-07-31T10:05:41.963Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #7","mintAddress":"4TpSEgXJnqovfZQsBcgR7L8wEfT7Syzud57ezpYh9fkj","timeTaken":"6.31s","timestamp":"2025-07-31T10:05:42.054Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #8","mintAddress":"3pvNFStE94M3dV1SJAnWYvz7DzhtKFktYW2mSyEenYMp","timeTaken":"6.57s","timestamp":"2025-07-31T10:05:42.217Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #16","mintAddress":"4mDvg99Sru5XDw5yQqSua1rSnBYR9Crz6AQdtpH5zCdj","timeTaken":"4.19s","timestamp":"2025-07-31T10:05:46.376Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #14","mintAddress":"7zepwxZ4RqU9pob5LrLEGXLBNUMENxYzz5nHQR83Vr29","timeTaken":"4.53s","timestamp":"2025-07-31T10:05:46.379Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #19","mintAddress":"372tn91kg68zw65CNkVrYxPEQqHSCbm2UWwZfVZ6jPgj","timeTaken":"4.31s","timestamp":"2025-07-31T10:05:46.648Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #15","mintAddress":"6aizjvndG2jhyMeSzHHLCAQB43TyaJNUKd6WxRtmk98S","timeTaken":"4.78s","timestamp":"2025-07-31T10:05:46.883Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #13","mintAddress":"4iXN9ikMpEM8moyvzA4PRtAALMZ1LcFVeaPz2fx9xnBa","timeTaken":"5.24s","timestamp":"2025-07-31T10:05:47.011Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #20","mintAddress":"5C5X6tkFncar3T99SEyCD25q4bQDjDeWqaaGWJYbR72L","timeTaken":"4.54s","timestamp":"2025-07-31T10:05:47.051Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #17","mintAddress":"FBjBgS69uuxcxNEDD8Z1xuJFN4wqtgyzzxg4X6xGPtJk","timeTaken":"4.87s","timestamp":"2025-07-31T10:05:47.264Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #12","mintAddress":"8SDfzDf8V1NqBxaxgD9MTdtQCadDFGYxiT8ErwhVf7jP","timeTaken":"5.44s","timestamp":"2025-07-31T10:05:47.268Z"}
{"cost":"0.196566 SOL","level":"info","message":"✅ Successfully minted: Test NFT #11","mintAddress":"9k658cymcREU6oJbK1eeFnas7LvAQi4fZwMbj6VUwWpP","timeTaken":"5.46s","timestamp":"2025-07-31T10:05:47.289Z"}
{"cost":"0.294848 SOL","level":"info","message":"✅ Successfully minted: Test NFT #18","mintAddress":"HryxVUJwKtJZKMRxr4rMEM8azCfRzw3LuN1bnXDy6Ccc","timeTaken":"10.30s","timestamp":"2025-07-31T10:05:52.535Z"}
{"cost":"0.098283 SOL","level":"info","message":"✅ Successfully minted: Test NFT #23","mintAddress":"Ei61XLMh6Jnnv6FcvUwnshGvPc7AoN2JyAPNFndFahKD","timeTaken":"14.06s","timestamp":"2025-07-31T10:06:01.565Z"}
{"cost":"0.098283 SOL","level":"info","message":"✅ Successfully minted: Test NFT #22","mintAddress":"DPMsBf4v8U4iGyT8wSdXNvh4Lkgx9wg2qXaqeMm5xoYT","timeTaken":"14.35s","timestamp":"2025-07-31T10:06:01.571Z"}
{"cost":"0.098283 SOL","level":"info","message":"✅ Successfully minted: Test NFT #24","mintAddress":"7Y4Zx4EYmdrhkyXDNNxEbS6tdqskgmoE8bRJ56bDGkc4","timeTaken":"13.77s","timestamp":"2025-07-31T10:06:01.579Z"}
{"cost":"0.098283 SOL","level":"info","message":"✅ Successfully minted: Test NFT #21","mintAddress":"EQxua9dsPRwnMW7PKVtLnnJfF5kEewe1HHzogot6ALet","timeTaken":"14.32s","timestamp":"2025-07-31T10:06:01.601Z"}
{"cost":"0.098283 SOL","level":"info","message":"✅ Successfully minted: Test NFT #25","mintAddress":"433dWgG2CkbCiwSN7KRZGvRWXQ75cTuGAATiGKZwsEbW","timeTaken":"13.73s","timestamp":"2025-07-31T10:06:01.945Z"}
{"avgCostPerNFT":"0.180840 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":1921,"successRate":"100.0%","timestamp":"2025-07-31T10:06:01.947Z","totalCost":"4.521009 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"46.84s"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T10:09:40.829Z"}
{"balance":"0.230818 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T10:11:25.573Z"}
{"batchSize":5,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T10:13:36.972Z","totalNFTs":5}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T10:13:36.981Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":5,"progress":"0.0%","timestamp":"2025-07-31T10:13:36.984Z","totalBatches":1}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #1","mintAddress":"B91Zy1Q99HMD9zuU1x374FJ1k3Ng8eqbVd6jnDrp1NSF","timeTaken":"12.33s","timestamp":"2025-07-31T10:14:05.364Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #2","mintAddress":"7SUAY3zDkWfpGzDjWQcTyTjfgh2BnRraReheV7wEdP54","timeTaken":"5.30s","timestamp":"2025-07-31T10:14:19.156Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #3","mintAddress":"DimoAW4iN8ZWakQej919VwayVZWSBvK1reez1Y1ZR4Le","timeTaken":"10.27s","timestamp":"2025-07-31T10:14:36.866Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #4","mintAddress":"CkkBNhsjckf58Ld7ct87pAkHkwZKucpxZCFSoMhVsZhf","timeTaken":"5.63s","timestamp":"2025-07-31T10:14:50.242Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Test NFT #5","mintAddress":"9ixmsxeHGjBBytAhCPKbyJWszumpPAzYgv5W8dhgwWRm","timeTaken":"19.21s","timestamp":"2025-07-31T10:15:18.358Z"}
{"cost":"0.098283 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":5,"timeTaken":"101.47s","timestamp":"2025-07-31T10:15:18.460Z"}
{"avgCostPerNFT":"0.019657 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":177,"successRate":"100.0%","timestamp":"2025-07-31T10:15:18.462Z","totalCost":"0.098283 SOL","totalFailed":0,"totalSuccessful":5,"totalTime":"101.50s"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T10:25:03.061Z"}
{"balance":"0.132535 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T10:30:16.985Z"}
{"batchSize":5,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T10:30:45.421Z","totalNFTs":5}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T10:30:45.424Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":5,"progress":"0.0%","timestamp":"2025-07-31T10:30:45.426Z","totalBatches":1}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: NFT1","mintAddress":"6eVMZQehAS95fYtBow9yb76Waw1jTnz2j9i5p65ZD41c","timeTaken":"17.01s","timestamp":"2025-07-31T10:31:17.242Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: NFT2","mintAddress":"GcrZQ8o7jkFAYJPJbtVDSdddywmzJ7oafoZc6v4w2Ccf","timeTaken":"21.62s","timestamp":"2025-07-31T10:31:47.853Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: NFT3","mintAddress":"J34FHc5QfR71zix7UZx66P7qvpPXHdkjztA3nNRbNJPQ","timeTaken":"11.75s","timestamp":"2025-07-31T10:32:10.281Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: NFT4","mintAddress":"5LHCmKrsYsryFDPfhn3hVhy1yi5vFUPyMgW6BBMxpcMK","timeTaken":"7.71s","timestamp":"2025-07-31T10:32:26.672Z"}
{"balance":"0.053909 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T10:36:08.914Z"}
{"batchSize":3,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T10:38:14.791Z","totalNFTs":3}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T10:38:14.797Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":3,"progress":"0.0%","timestamp":"2025-07-31T10:38:14.800Z","totalBatches":1}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N1","mintAddress":"CEdsLfCxXDscUVXkt8gzvspyreKBXofxX8TF5wDBdKFE","timeTaken":"7.08s","timestamp":"2025-07-31T10:38:22.402Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N2","mintAddress":"CKLT7MwF7zakzX1EQt8VckSeW7rP5a2THoGra7QMLxit","timeTaken":"3.92s","timestamp":"2025-07-31T10:38:26.802Z"}
{"level":"warn","message":"Mint attempt failed for N3: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [1] that translates to \"\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: InstructionPackError\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Transfer: insufficient lamports 14585960, need ********\n| Program ******************************** failed: custom program error: 0x1\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 9739 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\n","timestamp":"2025-07-31T10:38:29.325Z"}
{"level":"warn","message":"Mint attempt failed for N3: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [1] that translates to \"\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: InstructionPackError\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Transfer: insufficient lamports 14585960, need ********\n| Program ******************************** failed: custom program error: 0x1\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 9739 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\n","timestamp":"2025-07-31T10:38:30.657Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [1] that translates to \"\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: InstructionPackError\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Transfer: insufficient lamports 14585960, need ********\n| Program ******************************** failed: custom program error: 0x1\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 9739 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\n","level":"error","message":"Failed to mint NFT: N3","timestamp":"2025-07-31T10:38:30.664Z"}
{"cost":"0.039313 SOL","failed":1,"level":"info","message":"✅ Batch 1 completed","successRate":"66.7%","successful":2,"timeTaken":"15.96s","timestamp":"2025-07-31T10:38:30.769Z"}
{"avgCostPerNFT":"0.019657 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":450,"successRate":"66.7%","timestamp":"2025-07-31T10:38:30.776Z","totalCost":"0.039313 SOL","totalFailed":1,"totalSuccessful":2,"totalTime":"15.98s"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T11:30:19.576Z"}
{"balance":"0.014596 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T11:39:40.602Z"}
{"balance":"0.014596 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T11:40:11.715Z"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T11:40:48.931Z","totalNFTs":1}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T11:40:48.936Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T11:40:48.940Z","totalBatches":1}
{"level":"warn","message":"Mint attempt failed for N: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 0: custom program error: 0x1. \nLogs: \n[\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\",\n  \"Program log: IX: Create Metadata Accounts v3\",\n  \"Program ******************************** invoke [2]\",\n  \"Transfer: insufficient lamports ********, need ********\",\n  \"Program ******************************** failed: custom program error: 0x1\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 8398 of 200000 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.","timestamp":"2025-07-31T11:41:06.445Z"}
{"level":"warn","message":"Mint attempt failed for N: Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 0: custom program error: 0x1. \nLogs: \n[\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\",\n  \"Program log: IX: Create Metadata Accounts v3\",\n  \"Program ******************************** invoke [2]\",\n  \"Transfer: insufficient lamports 7549200, need ********\",\n  \"Program ******************************** failed: custom program error: 0x1\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 8398 of 200000 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.","timestamp":"2025-07-31T11:41:10.926Z"}
{"error":"Simulation failed. \nMessage: Transaction simulation failed: Error processing Instruction 0: custom program error: 0x1. \nLogs: \n[\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\",\n  \"Program log: IX: Create Metadata Accounts v3\",\n  \"Program ******************************** invoke [2]\",\n  \"Transfer: insufficient lamports 7549200, need ********\",\n  \"Program ******************************** failed: custom program error: 0x1\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 8398 of 200000 compute units\",\n  \"Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\"\n]. \nCatch the `SendTransactionError` and call `getLogs()` on it for full details.","level":"error","message":"Failed to mint NFT: N","timestamp":"2025-07-31T11:41:10.930Z"}
{"cost":"0.000000 SOL","failed":1,"level":"info","message":"✅ Batch 1 completed","successRate":"0.0%","successful":0,"timeTaken":"22.09s","timestamp":"2025-07-31T11:41:11.033Z"}
{"avgCostPerNFT":"0.000000 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":0,"successRate":"0.0%","timestamp":"2025-07-31T11:41:11.038Z","totalCost":"0.000000 SOL","totalFailed":1,"totalSuccessful":0,"totalTime":"22.11s"}
{"balance":"0.007554 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T11:45:21.577Z"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T11:46:09.129Z","totalNFTs":1}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T11:46:09.135Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T11:46:09.138Z","totalBatches":1}
{"level":"warn","message":"Mint attempt failed for N: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [1] that translates to \"\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: InstructionPackError\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Transfer: insufficient lamports 7544200, need ********\n| Program ******************************** failed: custom program error: 0x1\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 9621 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\n","timestamp":"2025-07-31T11:46:17.834Z"}
{"level":"warn","message":"Mint attempt failed for N: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [1] that translates to \"\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: InstructionPackError\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Transfer: insufficient lamports 7544200, need ********\n| Program ******************************** failed: custom program error: 0x1\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 9621 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\n","timestamp":"2025-07-31T11:46:19.499Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [1] that translates to \"\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: InstructionPackError\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Transfer: insufficient lamports 7544200, need ********\n| Program ******************************** failed: custom program error: 0x1\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 9621 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0x1\n","level":"error","message":"Failed to mint NFT: N","timestamp":"2025-07-31T11:46:19.501Z"}
{"cost":"0.000000 SOL","failed":1,"level":"info","message":"✅ Batch 1 completed","successRate":"0.0%","successful":0,"timeTaken":"10.46s","timestamp":"2025-07-31T11:46:19.603Z"}
{"avgCostPerNFT":"0.000000 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":0,"successRate":"0.0%","timestamp":"2025-07-31T11:46:19.604Z","totalCost":"0.000000 SOL","totalFailed":1,"totalSuccessful":0,"totalTime":"10.48s"}
{"balance":"1.007554 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T11:49:34.805Z"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T11:49:49.722Z","totalNFTs":1}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T11:49:49.725Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T11:49:49.728Z","totalBatches":1}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"55VSVkUNKKwGCemsc1FthZkiaFhUCVQ6sZGmtYRJ9rcv","timeTaken":"10.55s","timestamp":"2025-07-31T11:50:01.021Z"}
{"cost":"0.019657 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":1,"timeTaken":"11.39s","timestamp":"2025-07-31T11:50:01.124Z"}
{"avgCostPerNFT":"0.019657 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":316,"successRate":"100.0%","timestamp":"2025-07-31T11:50:01.125Z","totalCost":"0.019657 SOL","totalFailed":0,"totalSuccessful":1,"totalTime":"11.40s"}
{"balance":"0.987898 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T11:52:00.523Z"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T11:52:11.492Z","totalNFTs":25}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T11:52:11.494Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T11:52:11.495Z","totalBatches":1}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"5bRMGCXWDB11qG8EhhwPfz3inSpyoAhaK91btXHya8J3","timeTaken":"6.97s","timestamp":"2025-07-31T11:52:18.809Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"5V4EDgQ2kfXMxQykTcdqzvuhL7Bekdv8ksnTCkW46i2G","timeTaken":"4.21s","timestamp":"2025-07-31T11:52:23.512Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"EpWqrNZvXXyoPKpXmGKMRUCo77NH2Cd1W8eL71rEtTaU","timeTaken":"5.22s","timestamp":"2025-07-31T11:52:29.155Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"96MvmMEU56Fr355gH1SZf9t978PaurDDC6vJEWYCJ9GN","timeTaken":"3.86s","timestamp":"2025-07-31T11:52:33.399Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"3D3GbHTLCAH1S7JM9ZQkVKH92ESq3hVkprH5F4c4RcRJ","timeTaken":"4.15s","timestamp":"2025-07-31T11:52:38.008Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"DzmCPv8x4wYQorTsEnYiEmRcZzDWKFLHBp1q2MFRAtUW","timeTaken":"3.39s","timestamp":"2025-07-31T11:52:41.892Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"42VwD1vt5h3a59J2w8BVJjAKCM9h8fvWazKh1UGLGZrK","timeTaken":"5.81s","timestamp":"2025-07-31T11:52:48.100Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"FbEg9ZgwKNjBM7ZoYkrHjwDaWxGBHMzMQJMjeShK6Pf","timeTaken":"6.04s","timestamp":"2025-07-31T11:52:54.650Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"8EKsCS4A6R9d4xtGeXdbEPiGyYn7kTrB8C8TBH3hhpUc","timeTaken":"4.74s","timestamp":"2025-07-31T11:52:59.764Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"GKw5fVnUX6ZJah2WEfYPSYBD1zKvkjX7wi6Ga1u86WkF","timeTaken":"4.51s","timestamp":"2025-07-31T11:53:04.769Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"3aJhTjhKHY8qLsSMb6kXdbkLDVtvWiKp1R1Pfj7RVLBa","timeTaken":"3.70s","timestamp":"2025-07-31T11:53:08.847Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"9jDLQzLDHUHHgSb6T2GtaMsrKHocDAkXtvhgQ7X99okn","timeTaken":"4.08s","timestamp":"2025-07-31T11:53:13.318Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"33DSUGrXpYVVh3W9XdHP3HbKaRZeCSDFzLrQdhMY92Ds","timeTaken":"3.64s","timestamp":"2025-07-31T11:53:17.382Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"88QqcmtXkCTvp4CemfBGq6KEFvUThQ4Eovfy1Y8or2Bp","timeTaken":"3.18s","timestamp":"2025-07-31T11:53:21.049Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"5obyLBCvftvD8P5ya7XiT1SyWqaezjtWpE7HnrvGAyqt","timeTaken":"9.24s","timestamp":"2025-07-31T11:53:30.662Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"C6j4ER4ueYWLwhNesMAV92AiNBJafTRKhdPqjtxAmVFg","timeTaken":"3.19s","timestamp":"2025-07-31T11:53:34.231Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"8UJzRxJRHeQyqgfdpEvuHAuu8UZgMvsrgBRigLU39xDt","timeTaken":"4.06s","timestamp":"2025-07-31T11:53:38.778Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"DK9NMfVD3fkb6ukZdy5wXCvnSZk2G5kLRecPuH3gE6ZG","timeTaken":"5.11s","timestamp":"2025-07-31T11:53:44.271Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"JB69NgZqyyswgJXRMGWyQ2KTQGAyBNd3CJ8X5CmDbtYw","timeTaken":"4.18s","timestamp":"2025-07-31T11:53:48.835Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"3Ek4ZtwLJwXzod35Ti7W8KakBMk53RcKAZck1pg3bTki","timeTaken":"5.38s","timestamp":"2025-07-31T11:53:54.587Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"9y74bjx9XVomGgtFzpTrEeE7rYimk86yNmcuaddVKhYf","timeTaken":"3.15s","timestamp":"2025-07-31T11:53:58.118Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"EnZWpC6ETMeUvPqQfr4hoaKiPwt2a8jbD7Lv5m1vg18F","timeTaken":"3.75s","timestamp":"2025-07-31T11:54:02.232Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"ap7ETcB15pHGyR8Mo8pjvYSEEVdy9GFuEcD4e9Fdi83","timeTaken":"3.97s","timestamp":"2025-07-31T11:54:06.612Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"BUcjopQozYzJBSt3bLfpm3sNHciAXscj1pzR1jiD8yKs","timeTaken":"4.85s","timestamp":"2025-07-31T11:54:11.888Z"}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: N","mintAddress":"4HYGvgXmMsKcK9NSXyatZQKzhwAz1Wux9pR3sWx845Lh","timeTaken":"4.04s","timestamp":"2025-07-31T11:54:16.386Z"}
{"cost":"0.491414 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":25,"timeTaken":"124.99s","timestamp":"2025-07-31T11:54:16.490Z"}
{"avgCostPerNFT":"0.019657 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":720,"successRate":"100.0%","timestamp":"2025-07-31T11:54:16.492Z","totalCost":"0.491414 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"125.00s"}
{"balance":"0.496484 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T11:56:06.208Z"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T11:56:59.025Z","totalNFTs":1}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T11:56:59.031Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T11:56:59.033Z","totalBatches":1}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: ","mintAddress":"2fB7Czq74ZwBtz7WonLfQ7BqZRxDykP1sccCcSCBLp3B","timeTaken":"12.52s","timestamp":"2025-07-31T11:57:11.879Z"}
{"cost":"0.019657 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":1,"timeTaken":"12.95s","timestamp":"2025-07-31T11:57:11.981Z"}
{"avgCostPerNFT":"0.019657 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":278,"successRate":"100.0%","timestamp":"2025-07-31T11:57:11.984Z","totalCost":"0.019657 SOL","totalFailed":0,"totalSuccessful":1,"totalTime":"12.96s"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T12:07:44.208Z","totalNFTs":1}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:07:44.221Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T12:07:44.229Z","totalBatches":1}
{"assetAddress":"7fi6ci7W3A9YdK8XoG8TE1S5rZmViMp47d2x4gdgQ8Qh","cost":"0.002923 SOL","level":"info","message":"✅ Successfully minted Core NFT: ","timeTaken":"4.07s","timestamp":"2025-07-31T12:07:48.825Z"}
{"cost":"0.002923 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":1,"timeTaken":"4.07s","timestamp":"2025-07-31T12:07:48.828Z"}
{"avgCostPerNFT":"0.002923 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":777,"successRate":"100.0%","timestamp":"2025-07-31T12:07:48.832Z","totalCost":"0.002923 SOL","totalFailed":0,"totalSuccessful":1,"totalTime":"4.63s"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T12:09:16.325Z"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T12:13:00.836Z","totalNFTs":1}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:13:00.844Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T12:13:00.849Z","totalBatches":1}
{"level":"warn","message":"Core mint attempt failed for Awesome NFT #1: Simulation failed. \nMessage: base64 encoded solana_transaction::versioned::VersionedTransaction too large: 1728 bytes (max: encoded/raw 1644/1232). \n\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.","timestamp":"2025-07-31T12:13:03.316Z"}
{"level":"warn","message":"Core mint attempt failed for Awesome NFT #1: Simulation failed. \nMessage: base64 encoded solana_transaction::versioned::VersionedTransaction too large: 1728 bytes (max: encoded/raw 1644/1232). \n\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.","timestamp":"2025-07-31T12:13:04.360Z"}
{"error":"Simulation failed. \nMessage: base64 encoded solana_transaction::versioned::VersionedTransaction too large: 1728 bytes (max: encoded/raw 1644/1232). \n\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.","level":"error","message":"Failed to mint Core NFT: Awesome NFT #1","timestamp":"2025-07-31T12:13:04.379Z"}
{"cost":"0.000000 SOL","failed":1,"level":"info","message":"✅ Batch 1 completed","successRate":"0.0%","successful":0,"timeTaken":"0.00s","timestamp":"2025-07-31T12:13:04.397Z"}
{"avgCostPerNFT":"0.000000 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":0,"successRate":"0.0%","timestamp":"2025-07-31T12:13:04.418Z","totalCost":"0.000000 SOL","totalFailed":1,"totalSuccessful":0,"totalTime":"3.58s"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T12:15:16.057Z","totalNFTs":1}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:15:16.066Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T12:15:16.071Z","totalBatches":1}
{"level":"warn","message":"Metadata URI too long (260), using minimal version","timestamp":"2025-07-31T12:15:17.168Z"}
{"assetAddress":"3Qvf127TRBGS2EW2SJfmqaYukZLDgGVSJ6kZAXeohaHM","cost":"0.003382 SOL","level":"info","message":"✅ Successfully minted Core NFT: Core NFT #1","timeTaken":"3.98s","timestamp":"2025-07-31T12:15:20.420Z"}
{"cost":"0.003382 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":1,"timeTaken":"3.98s","timestamp":"2025-07-31T12:15:20.421Z"}
{"avgCostPerNFT":"0.003382 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":822,"successRate":"100.0%","timestamp":"2025-07-31T12:15:20.423Z","totalCost":"0.003382 SOL","totalFailed":0,"totalSuccessful":1,"totalTime":"4.38s"}
{"balance":"0.470522 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T12:18:38.652Z"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T12:18:45.820Z","totalNFTs":1}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:18:45.829Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T12:18:45.833Z","totalBatches":1}
{"level":"warn","message":"Mint attempt failed for Regular NFT #1: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [13] that translates to \"URI too long\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: UriTooLong: URI too long\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 380177 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: URI too long\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 38385 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0xd\n","timestamp":"2025-07-31T12:18:51.601Z"}
{"level":"warn","message":"Mint attempt failed for Regular NFT #1: The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [13] that translates to \"URI too long\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: UriTooLong: URI too long\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 380177 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: URI too long\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 38385 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0xd\n","timestamp":"2025-07-31T12:18:53.651Z"}
{"error":"The program [TokenMetadataProgram] at address [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s] raised an error of code [13] that translates to \"URI too long\".\n\nSource: Program > TokenMetadataProgram [metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s]\n\nCaused By: UriTooLong: URI too long\n\nProgram Logs:\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s invoke [1]\n| Program log: IX: Create\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]\n| Program log: Instruction: InitializeMint2\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2828 of 380177 compute units\n| Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success\n| Program log: Allocate space for the account\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: Assign the account to the owning program\n| Program ******************************** invoke [2]\n| Program ******************************** success\n| Program log: URI too long\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s consumed 38385 of 400000 compute units\n| Program metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s failed: custom program error: 0xd\n","level":"error","message":"Failed to mint NFT: Regular NFT #1","timestamp":"2025-07-31T12:18:53.656Z"}
{"cost":"0.000000 SOL","failed":1,"level":"info","message":"✅ Batch 1 completed","successRate":"0.0%","successful":0,"timeTaken":"7.92s","timestamp":"2025-07-31T12:18:53.764Z"}
{"avgCostPerNFT":"0.000000 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":0,"successRate":"0.0%","timestamp":"2025-07-31T12:18:53.776Z","totalCost":"0.000000 SOL","totalFailed":1,"totalSuccessful":0,"totalTime":"7.95s"}
{"balance":"0.470522 SOL","level":"info","message":"Wallet validation successful","publicKey":"6Ma8iCSr5Cu6n92cqjPcNkPZLmvBAgU1nzSHDLs2bKhq","timestamp":"2025-07-31T12:20:25.415Z"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting NFT minting process","timestamp":"2025-07-31T12:20:42.471Z","totalNFTs":1}
{"level":"info","message":"💰 Processing NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:20:42.475Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T12:20:42.478Z","totalBatches":1}
{"cost":"0.019657 SOL","level":"info","message":"✅ Successfully minted: Regular NFT #1","mintAddress":"72axWe6PpEjJ36Zf4zBH3zfENxgFV7wPjcPD8zZzuNeC","timeTaken":"13.40s","timestamp":"2025-07-31T12:20:56.310Z"}
{"cost":"0.019657 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":1,"timeTaken":"13.93s","timestamp":"2025-07-31T12:20:56.416Z"}
{"avgCostPerNFT":"0.019657 SOL","level":"info","message":"🎉 Minting process completed!","nftsPerHour":258,"successRate":"100.0%","timestamp":"2025-07-31T12:20:56.418Z","totalCost":"0.019657 SOL","totalFailed":0,"totalSuccessful":1,"totalTime":"13.95s"}
{"batchSize":1,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T12:24:27.416Z","totalNFTs":1}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:24:27.425Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":1,"progress":"0.0%","timestamp":"2025-07-31T12:24:27.430Z","totalBatches":1}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:24:29.332Z"}
{"assetAddress":"Cwci1zDS3n5DZgHtNSkrxyB4pDqJ9QefH2H8nHsUTLKH","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #1","timeTaken":"6.54s","timestamp":"2025-07-31T12:24:34.322Z"}
{"cost":"0.003424 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":1,"timeTaken":"6.54s","timestamp":"2025-07-31T12:24:34.328Z"}
{"avgCostPerNFT":"0.003424 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":520,"successRate":"100.0%","timestamp":"2025-07-31T12:24:34.335Z","totalCost":"0.003424 SOL","totalFailed":0,"totalSuccessful":1,"totalTime":"6.93s"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T12:29:45.913Z","totalNFTs":25}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:29:45.917Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T12:29:45.919Z","totalBatches":1}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:29:46.334Z"}
{"assetAddress":"Ar2DfQQimHkGvtmf2j1sinLBrmhRpoaaPWPDuNFpgwLd","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #1","timeTaken":"2.77s","timestamp":"2025-07-31T12:29:49.150Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:29:50.231Z"}
{"assetAddress":"8nVzvJvCEyGfqG4BnWw5P9pc4x8R5VUmPENhEjfMbANA","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #2","timeTaken":"4.09s","timestamp":"2025-07-31T12:29:53.581Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:29:53.886Z"}
{"assetAddress":"HwEWr2XuW4wVDbmTNgmqBtA3uP9gyPDBaQCfvedtq6DG","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #3","timeTaken":"3.03s","timestamp":"2025-07-31T12:29:56.931Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:29:57.306Z"}
{"assetAddress":"7BRAxhmLyXVTacksuVmF4afV2DcmqXTxxfcccFACYTJN","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #4","timeTaken":"3.80s","timestamp":"2025-07-31T12:30:00.996Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:30:01.316Z"}
{"assetAddress":"9me7CEYZfoBCehm4QdNnDuEbbYKavsD4du61S8vBke6","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #5","timeTaken":"3.06s","timestamp":"2025-07-31T12:30:04.919Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:30:05.208Z"}
{"assetAddress":"CVXywFCaTMY2ejKDdXPpyDDA3FTrMrHeGEA1vBHQmUpV","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #6","timeTaken":"3.00s","timestamp":"2025-07-31T12:30:08.192Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:30:08.570Z"}
{"assetAddress":"BjYE6iaDkBchV1UrMGzjKdqvBgkXJ5cdXxUFEXZvkcu4","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #7","timeTaken":"3.32s","timestamp":"2025-07-31T12:30:11.812Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:30:12.211Z"}
{"assetAddress":"GYffiTZhkEfveWmbCvQnfzALMz2rPsWnz63osyA4Rqhx","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #8","timeTaken":"3.68s","timestamp":"2025-07-31T12:30:15.863Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:30:16.191Z"}
{"assetAddress":"EWwHRAhsGUpQA2poaeYeY4uwW2ceD2odF9xAiNWnqUkQ","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #9","timeTaken":"2.88s","timestamp":"2025-07-31T12:30:19.622Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:19.913Z"}
{"assetAddress":"HhDKTeigGxk8RD48ZVsTDzhhTEuQ65mcWFfeNV4ba5GZ","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #10","timeTaken":"8.83s","timestamp":"2025-07-31T12:30:28.810Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:29.220Z"}
{"assetAddress":"HPKqFmAxz8fetHauMRAoRdZkgKns58YgZXJguyFmrKTn","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #11","timeTaken":"4.13s","timestamp":"2025-07-31T12:30:33.618Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:34.056Z"}
{"assetAddress":"7LDnvbfDnFAwp4SrgAkDpTeR4X51S6p17wLy2XUxKA61","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #12","timeTaken":"3.38s","timestamp":"2025-07-31T12:30:37.299Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:37.604Z"}
{"assetAddress":"G8yzHNPpgSiWYdXa4gsXeq135EJt2C4Tnxz82F7FW56y","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #13","timeTaken":"3.64s","timestamp":"2025-07-31T12:30:41.510Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:42.439Z"}
{"assetAddress":"DsvruBjoaLXaqAAmPu4ps4wdikMtRMqoGTtJeBrFaidX","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #14","timeTaken":"3.73s","timestamp":"2025-07-31T12:30:45.517Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:45.922Z"}
{"assetAddress":"EQtoQi1PUCvRSyFd1y4SFdoucvNgJTXb7XKtDvVZkgMa","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #15","timeTaken":"3.31s","timestamp":"2025-07-31T12:30:50.042Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:50.432Z"}
{"assetAddress":"9885pXDGKaStzQ18nUcjMY6SNbQ348ZrfpWRn5F6RrJG","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #16","timeTaken":"4.08s","timestamp":"2025-07-31T12:30:54.405Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:54.877Z"}
{"assetAddress":"EaPxhwDkXJ25pfQspEvWHrhTqDXuSgkrPgFDQTLPkLHT","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #17","timeTaken":"2.79s","timestamp":"2025-07-31T12:30:58.039Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:30:58.328Z"}
{"assetAddress":"GeCUNRdySzfWmYbDWpwheERsyi84AFYBKefhmoGErssn","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #18","timeTaken":"2.67s","timestamp":"2025-07-31T12:31:01.414Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:31:01.801Z"}
{"assetAddress":"3vEtn8WwHVtC6eHSWJcfH3BZtEMSNQhr5csu9c165yJr","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #19","timeTaken":"5.29s","timestamp":"2025-07-31T12:31:07.038Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:31:07.346Z"}
{"assetAddress":"2kfZdxpHe58sY4bjtTLSXKPjD8WUjMLmMC8quUdf6xj5","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #20","timeTaken":"2.51s","timestamp":"2025-07-31T12:31:09.811Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:31:10.807Z"}
{"assetAddress":"vnPRWWvgX9F2715GCu1kHQAssLeADzSWpA9UZoTeiE2","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #21","timeTaken":"3.77s","timestamp":"2025-07-31T12:31:13.911Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:31:14.191Z"}
{"assetAddress":"7Ai6N2kmf2ev6DLz9ESWgJ67qmgzQ312jsnXKTa9sn7r","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #22","timeTaken":"3.94s","timestamp":"2025-07-31T12:31:18.144Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:31:18.510Z"}
{"assetAddress":"8Kxdtyp7LMz3PaFB38tqkrLBK3Ymxr8XRVwvhaPgcrBa","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #23","timeTaken":"7.10s","timestamp":"2025-07-31T12:31:25.641Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:31:25.923Z"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T12:34:08.830Z","totalNFTs":25}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:34:08.837Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T12:34:08.843Z","totalBatches":1}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:13.345Z"}
{"assetAddress":"BC4QLnmUwK8ggJMojr8qR8WXTWXdXLXgC3N7tMGyky7F","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #1","timeTaken":"7.52s","timestamp":"2025-07-31T12:34:16.731Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:18.026Z"}
{"assetAddress":"3BPExVVNcBe5xSVKeFNFwDVaZGJMM4g8JG2GWg9fHNtb","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #2","timeTaken":"5.58s","timestamp":"2025-07-31T12:34:22.802Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:23.227Z"}
{"assetAddress":"1DJwV38PBQ3DnBnRKFzUb8wswXTo5fEEPrhUxcurrfb","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #3","timeTaken":"6.47s","timestamp":"2025-07-31T12:34:30.439Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:30.853Z"}
{"assetAddress":"3EucoxCHkk9cgDc5nVgiS9MMgWg6rJSxroKBJUZJCBvL","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #4","timeTaken":"3.31s","timestamp":"2025-07-31T12:34:34.051Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:34.474Z"}
{"assetAddress":"55qX8t94y8Bj8ypeZvC12A3SYycVVjN5Ngknob1B5YwT","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #5","timeTaken":"3.67s","timestamp":"2025-07-31T12:34:38.158Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:38.632Z"}
{"assetAddress":"477n5itHLP2ZruwGmgaBs2SpueLf2WJkLnrJjdLH7P4J","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #6","timeTaken":"2.63s","timestamp":"2025-07-31T12:34:41.260Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:41.640Z"}
{"assetAddress":"FWSzJbzhP4FANzweLqiU87vZcp4c7jPP6bM3iwqDs4r8","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #7","timeTaken":"3.88s","timestamp":"2025-07-31T12:34:45.553Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:45.883Z"}
{"assetAddress":"4ssuqKmqNgEDCQ6gSdG3DzD9FBf77jsfAbqjgTyC1NV4","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #8","timeTaken":"3.29s","timestamp":"2025-07-31T12:34:49.134Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T12:34:49.434Z"}
{"assetAddress":"7bzE6uGY6YuPN8k87iuerqB1hH6uVRMf4EJBjNEqgsC6","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #9","timeTaken":"2.84s","timestamp":"2025-07-31T12:34:52.480Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:34:52.794Z"}
{"assetAddress":"XpKd4aqob5eB4P5usoyBdTanwmyVAUnr8cN983yRmCk","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #10","timeTaken":"3.36s","timestamp":"2025-07-31T12:34:56.128Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:34:56.434Z"}
{"assetAddress":"BXEUsZ4CjYmGbQzzETMHYMHrL6Ddy3Ux2ai872Q1dSGh","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #11","timeTaken":"3.51s","timestamp":"2025-07-31T12:34:59.967Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:01.076Z"}
{"assetAddress":"6wYb5DHBv62tLd4woYkv5BizRvDHkUpWabr5HRzvZQTd","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #12","timeTaken":"3.68s","timestamp":"2025-07-31T12:35:04.067Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:05.793Z"}
{"assetAddress":"A7PrRrYbwg8twb4Bx4rZAYuuh7asykjK3FNpb67VxCi1","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #13","timeTaken":"6.24s","timestamp":"2025-07-31T12:35:10.646Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:10.939Z"}
{"assetAddress":"E2TTU9G59Yf1yBXgXMW8qvmzFsGBvUSeaNuRxzgUNnax","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #14","timeTaken":"9.89s","timestamp":"2025-07-31T12:35:20.849Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:21.191Z"}
{"assetAddress":"3BxsPWRbvoTFymYD5grYCXnjxjQc2pcwfc2xibcHWviU","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #15","timeTaken":"2.66s","timestamp":"2025-07-31T12:35:23.816Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:24.169Z"}
{"assetAddress":"2Ph7W3F5QELRum6y6e3eZGZVZ3cLCuWUBaKhVcuc6129","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #16","timeTaken":"4.06s","timestamp":"2025-07-31T12:35:28.218Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:28.528Z"}
{"assetAddress":"9Yo1Gm8Wpy7FzLv5T1nNs31RwUyRPrGa5go6D4rqy7r9","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #17","timeTaken":"3.09s","timestamp":"2025-07-31T12:35:31.596Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:31.937Z"}
{"assetAddress":"HdGPpnEng4FiGHxCQvzX2kAHmMieu5mRrNvh8uUrgTyt","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #18","timeTaken":"2.55s","timestamp":"2025-07-31T12:35:34.520Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:34.802Z"}
{"assetAddress":"8KaS99p6rCpDEKH6pGt7ZEY1GkthWAxHdmUCunQ5y3Aq","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #19","timeTaken":"2.92s","timestamp":"2025-07-31T12:35:37.846Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:38.714Z"}
{"assetAddress":"BaDSujhGztCQayD36EKpVtBHdjNH7EfzbSqdxt95zrst","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #20","timeTaken":"4.59s","timestamp":"2025-07-31T12:35:42.760Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:43.694Z"}
{"assetAddress":"44b6uotS5S2HGBvTM62BW9AEAwq3f8e195e6CzCBnFjc","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #21","timeTaken":"3.39s","timestamp":"2025-07-31T12:35:46.462Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:46.762Z"}
{"assetAddress":"FFA4mWfuSX4avTwV9Fbw4MMT6beb7CTh4bXE1Kxuz4pU","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #22","timeTaken":"9.28s","timestamp":"2025-07-31T12:35:56.349Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:35:57.268Z"}
{"assetAddress":"74oiEtxK8xxnhEyLhg4sSBzrX2E53r68mvANE2BDUTNh","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #23","timeTaken":"5.20s","timestamp":"2025-07-31T12:36:02.430Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:36:02.831Z"}
{"assetAddress":"4SLLf2sctB8WDpsNr3rV4Ld87BK4SSvdqSpAKwsLdAsU","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #24","timeTaken":"2.98s","timestamp":"2025-07-31T12:36:05.694Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T12:36:06.158Z"}
{"assetAddress":"BwxEVYTcgHXnjYVf8pjXayLkcRq2MgL9caqssn6zx9Uu","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #25","timeTaken":"2.90s","timestamp":"2025-07-31T12:36:09.175Z"}
{"cost":"0.085823 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":25,"timeTaken":"109.47s","timestamp":"2025-07-31T12:36:09.191Z"}
{"avgCostPerNFT":"0.003433 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":748,"successRate":"100.0%","timestamp":"2025-07-31T12:36:09.221Z","totalCost":"0.085823 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"120.37s"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T12:37:23.165Z"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T12:58:47.096Z","totalNFTs":25}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T12:58:47.098Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T12:58:47.100Z","totalBatches":1}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T12:58:47.407Z"}
{"assetAddress":"BnZShMrZoA7LaRU1mheZUBAXXM46GKVTB4AYLh1tVYto","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #1","timeTaken":"5.96s","timestamp":"2025-07-31T12:58:53.364Z"}
{"level":"warn","message":"Metadata URI too long (314), using minimal version","timestamp":"2025-07-31T12:58:53.725Z"}
{"assetAddress":"CbpFCPzbP2QfQvp5cRbrB9HxNoT1SgQ9HQJXadApJVPm","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #2","timeTaken":"3.74s","timestamp":"2025-07-31T12:58:57.576Z"}
{"level":"warn","message":"Metadata URI too long (314), using minimal version","timestamp":"2025-07-31T12:58:57.899Z"}
{"assetAddress":"9yN5ix4Sm1iV9LReyqGzyVmsF2fvuZv2PfxQfkegAHQF","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #3","timeTaken":"3.67s","timestamp":"2025-07-31T12:59:01.573Z"}
{"level":"warn","message":"Metadata URI too long (312), using minimal version","timestamp":"2025-07-31T12:59:02.008Z"}
{"assetAddress":"3Bz2D9aqAXV8UebJ3nQBaToJMY2ziuT9Hb6YFpWfXBBi","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #4","timeTaken":"3.16s","timestamp":"2025-07-31T12:59:05.060Z"}
{"level":"warn","message":"Metadata URI too long (314), using minimal version","timestamp":"2025-07-31T12:59:05.377Z"}
{"assetAddress":"GKuBGAK9WZ88VLkGpKdjTRUG5oQjgwEmTLD6khDNhRkK","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #5","timeTaken":"5.00s","timestamp":"2025-07-31T12:59:10.366Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T12:59:10.711Z"}
{"assetAddress":"A51S8mTJPNywUrZVp1nM14ygt967pCt21aH6nbmxNnqF","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #6","timeTaken":"5.74s","timestamp":"2025-07-31T12:59:16.518Z"}
{"level":"warn","message":"Metadata URI too long (312), using minimal version","timestamp":"2025-07-31T12:59:17.021Z"}
{"assetAddress":"7PnNdVYuFVxGEdAPkyTqWAFYjgji76mff26WwYuBCW6G","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #7","timeTaken":"3.24s","timestamp":"2025-07-31T12:59:20.194Z"}
{"level":"warn","message":"Metadata URI too long (314), using minimal version","timestamp":"2025-07-31T12:59:20.514Z"}
{"assetAddress":"Lca9MrhRC2oxB8oZ33tE7ZYfDc7ZEuSBzbPq1ZkVQeo","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #8","timeTaken":"3.17s","timestamp":"2025-07-31T12:59:23.703Z"}
{"level":"warn","message":"Metadata URI too long (314), using minimal version","timestamp":"2025-07-31T12:59:24.114Z"}
{"assetAddress":"DT9gYjL8eKCHDfHWGuhEC5phNmqfesoVHnqoMGvWWAP1","cost":"0.003410 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #9","timeTaken":"3.77s","timestamp":"2025-07-31T12:59:27.950Z"}
{"level":"warn","message":"Metadata URI too long (315), using minimal version","timestamp":"2025-07-31T12:59:28.969Z"}
{"assetAddress":"DjakA3FtWohXC4qcgWMJhai8AzFmeg6gKrXTCoA4NvpM","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #10","timeTaken":"4.40s","timestamp":"2025-07-31T12:59:32.651Z"}
{"level":"warn","message":"Metadata URI too long (320), using minimal version","timestamp":"2025-07-31T12:59:33.007Z"}
{"assetAddress":"AZXpXijbBie4pFSP3x8eNktL6DmSAGVZjuQEuCmq1HMb","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #11","timeTaken":"3.62s","timestamp":"2025-07-31T12:59:36.583Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T12:59:36.921Z"}
{"assetAddress":"4j6adqAfmqR4vjtGsyZF8Q3xKnJzaib5eA33k5xS3kEo","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #12","timeTaken":"2.93s","timestamp":"2025-07-31T12:59:39.816Z"}
{"level":"warn","message":"Metadata URI too long (315), using minimal version","timestamp":"2025-07-31T12:59:40.352Z"}
{"assetAddress":"AkFMhyQTpsyEHG9YaxAq3TmxbPocovMFiyHrVRrYUdNG","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #13","timeTaken":"3.54s","timestamp":"2025-07-31T12:59:43.810Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T12:59:44.191Z"}
{"assetAddress":"3Peqy4YuN2X9fVVibnBaKzWSVR1rwWZnkSgZj4BkK2g2","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #14","timeTaken":"3.26s","timestamp":"2025-07-31T12:59:47.352Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T12:59:48.313Z"}
{"assetAddress":"DFYUCyR5WPD1m3p1cah34sCk7QTyYpCVxj1fujywtSAE","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #15","timeTaken":"3.60s","timestamp":"2025-07-31T12:59:51.427Z"}
{"level":"warn","message":"Metadata URI too long (320), using minimal version","timestamp":"2025-07-31T12:59:51.725Z"}
{"assetAddress":"2cWrCeKGsSogBxTrwmYSCXMjpAKsMYUtiav9ek63k5YC","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #16","timeTaken":"3.42s","timestamp":"2025-07-31T12:59:55.231Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T12:59:55.537Z"}
{"assetAddress":"3Nj4vwsWznrnXaUeHzfoJBKzudsgfosXfdQFPHdGyZx9","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #17","timeTaken":"2.88s","timestamp":"2025-07-31T12:59:58.417Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T12:59:59.409Z"}
{"assetAddress":"A2UKjnU8Bcg2d5ME1JevMYcwy658QHCbmqVnAQks1QWg","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #18","timeTaken":"6.56s","timestamp":"2025-07-31T13:00:05.363Z"}
{"level":"warn","message":"Metadata URI too long (315), using minimal version","timestamp":"2025-07-31T13:00:05.654Z"}
{"assetAddress":"D3jVJPfXMYPbfC12QTgTpu6TXW9CkBFE6UrYgSA3hGbc","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #19","timeTaken":"3.51s","timestamp":"2025-07-31T13:00:09.247Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T13:00:09.568Z"}
{"assetAddress":"6HGP7e4APx1nRLtEMRhRP7dmZpdEzKy9wF48Dwi9Qq6Z","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #20","timeTaken":"3.22s","timestamp":"2025-07-31T13:00:12.986Z"}
{"level":"warn","message":"Metadata URI too long (320), using minimal version","timestamp":"2025-07-31T13:00:13.275Z"}
{"assetAddress":"D5w6LuAjC8sXDAp9x9AH5cFF6F5UZFYUMKVovEtzKgMu","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #21","timeTaken":"4.39s","timestamp":"2025-07-31T13:00:17.677Z"}
{"level":"warn","message":"Metadata URI too long (315), using minimal version","timestamp":"2025-07-31T13:00:18.033Z"}
{"assetAddress":"ukJQVXqbe5jDtHBULnecejESRHZdL48NjZf9H36JECG","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #22","timeTaken":"12.03s","timestamp":"2025-07-31T13:00:30.033Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T13:00:30.470Z"}
{"assetAddress":"8aPK54gRDFonT6CoZeVh5EWGQcJPh4uJutVgpYmkpAix","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #23","timeTaken":"6.80s","timestamp":"2025-07-31T13:00:37.231Z"}
{"level":"warn","message":"Metadata URI too long (317), using minimal version","timestamp":"2025-07-31T13:00:37.573Z"}
{"assetAddress":"57H3n9yoww7G7xu8uS6zBAe6Noc9UU4aBxxQPNoXyFZx","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #24","timeTaken":"4.49s","timestamp":"2025-07-31T13:00:42.021Z"}
{"level":"warn","message":"Metadata URI too long (315), using minimal version","timestamp":"2025-07-31T13:00:42.450Z"}
{"assetAddress":"HUDotkvjVwP2MqGD2rBDPth2L3Sfz6HMi6WMUe4f6hA3","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Client NFT #25","timeTaken":"2.74s","timestamp":"2025-07-31T13:00:45.062Z"}
{"cost":"0.085475 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":25,"timeTaken":"108.84s","timestamp":"2025-07-31T13:00:45.065Z"}
{"avgCostPerNFT":"0.003419 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":763,"successRate":"100.0%","timestamp":"2025-07-31T13:00:45.067Z","totalCost":"0.085475 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"117.97s"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T13:07:25.310Z"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T13:10:25.430Z"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T13:42:57.919Z","totalNFTs":25}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T13:42:57.922Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T13:42:57.924Z","totalBatches":1}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:42:58.342Z"}
{"assetAddress":"57KddyA5FDmFJaEjnonhvYhhapPjjr2wGbTddcBRurwy","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #1","timeTaken":"4.09s","timestamp":"2025-07-31T13:43:02.415Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:02.898Z"}
{"assetAddress":"CKV3DSXQW5jiF4okjvX5w695zjTbkHv1LhRwrEizvNvW","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #2","timeTaken":"3.39s","timestamp":"2025-07-31T13:43:06.232Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:06.693Z"}
{"assetAddress":"YSfAmx3hWewdmDDEi9fVtpgxho9N5jcM2JmzkyK3jTy","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #3","timeTaken":"4.51s","timestamp":"2025-07-31T13:43:11.783Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:12.176Z"}
{"assetAddress":"3PXasbnyHBx5hqpb6VQ8cPtPxpsykbtbDwvmDNPxoKwj","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #4","timeTaken":"3.02s","timestamp":"2025-07-31T13:43:15.188Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:15.561Z"}
{"assetAddress":"8zwZPVpdbo6jAtWgWboNgPDDGVx1ZZvMQ483iUdAZc33","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #5","timeTaken":"3.08s","timestamp":"2025-07-31T13:43:18.684Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:19.085Z"}
{"assetAddress":"AoKSGp5B6H4JsaMqnNUm5WW28eTDuZa4vKkD7EEj5sfo","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #6","timeTaken":"2.86s","timestamp":"2025-07-31T13:43:21.955Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:22.331Z"}
{"assetAddress":"6AQEvxzwgQHUEi5gcVjkA3SAd1fjXxCkdU4ZpxXwjNVx","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #7","timeTaken":"4.71s","timestamp":"2025-07-31T13:43:27.079Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:28.330Z"}
{"assetAddress":"GqDDm2NfmP9bC2sKx7qrcvE2VdrDzMoNrP8gEcELMURN","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #8","timeTaken":"3.67s","timestamp":"2025-07-31T13:43:31.136Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T13:43:31.543Z"}
{"assetAddress":"5FUC27DAhVQs8zvD89vXp9ZvuBpqotfRiqibS6iDGM3Z","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #9","timeTaken":"3.72s","timestamp":"2025-07-31T13:43:36.431Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:43:39.942Z"}
{"assetAddress":"52xHu2Ch92rSxXKUrbPqvCQMbsCcPQDUab5NgyiMB778","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #10","timeTaken":"8.30s","timestamp":"2025-07-31T13:43:45.132Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:43:45.541Z"}
{"assetAddress":"XSAUVECE7eCuxZRfA84KZ5846qh6EwszPyfgYj7nbY9","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #11","timeTaken":"3.68s","timestamp":"2025-07-31T13:43:49.211Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:43:49.641Z"}
{"assetAddress":"GiNv4nLW1gjgiqJ6GwQPMT7AnMyxzYjbWCQkdiKtTgd7","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #12","timeTaken":"4.52s","timestamp":"2025-07-31T13:43:54.105Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:43:54.487Z"}
{"assetAddress":"3tJeHSAUJ7TTL1hAaJhp5eMUGVjtW9AwJMvTiTTZpvYo","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #13","timeTaken":"18.02s","timestamp":"2025-07-31T13:44:13.357Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:13.776Z"}
{"assetAddress":"CQNVn5Uz2RmH6hNcMJFzM2UBJfHLgs54fQypSnVQw5Um","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #14","timeTaken":"6.26s","timestamp":"2025-07-31T13:44:20.117Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:20.522Z"}
{"assetAddress":"4UERGbK4CikbtcHt9dpfwV7vsARYfa2z4ch2jmQVn1JT","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #15","timeTaken":"3.30s","timestamp":"2025-07-31T13:44:23.833Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:24.229Z"}
{"assetAddress":"Cjfb5oEDfb7ZSrScNaMWXKsDyY2B37HPNTtbw3ddFecM","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #16","timeTaken":"4.88s","timestamp":"2025-07-31T13:44:29.141Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:29.765Z"}
{"assetAddress":"********************************************","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #17","timeTaken":"5.02s","timestamp":"2025-07-31T13:44:35.817Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:36.521Z"}
{"assetAddress":"Gg6vb8b7ewXGixY8vBKybfZ45Ny3L2vachWcWkJar3JW","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #18","timeTaken":"4.47s","timestamp":"2025-07-31T13:44:40.994Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:41.420Z"}
{"assetAddress":"EYUfNKS2pe5N1TkUtjk992MvWo8m4LdDfu2VhZEi8KYd","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #19","timeTaken":"6.48s","timestamp":"2025-07-31T13:44:47.891Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:48.382Z"}
{"assetAddress":"5moJHkAUumqqL4sPXQMn5Z68tJM56vLP9qdS7Xs9ydpT","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #20","timeTaken":"4.87s","timestamp":"2025-07-31T13:44:53.139Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:44:56.942Z"}
{"assetAddress":"7b1BVe7MszFLpNH7Mqtd22wgimakJjHASNDLEovxRPKQ","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #21","timeTaken":"9.73s","timestamp":"2025-07-31T13:45:03.297Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:45:04.766Z"}
{"assetAddress":"44qPbNnV8GoFYShd4W5RscQzgsuqGsjC5A9dHBziSvfY","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #22","timeTaken":"5.61s","timestamp":"2025-07-31T13:45:09.283Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:45:09.693Z"}
{"assetAddress":"GdujmkCWRohCRayQvtQhfaVuzb7wuH7MyDwpNHNrSEy","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #23","timeTaken":"3.57s","timestamp":"2025-07-31T13:45:13.292Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:45:13.716Z"}
{"assetAddress":"25rgQ2zMZQq8d4bLAwexU7ZK2axA9wv8wSTogq51HYCj","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #24","timeTaken":"8.97s","timestamp":"2025-07-31T13:45:22.646Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T13:45:23.032Z"}
{"assetAddress":"35ytkWxSoKZEQVUBZWXb1v6RW4MXsbPy1EKkG37kqpq5","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #25","timeTaken":"2.70s","timestamp":"2025-07-31T13:45:25.758Z"}
{"cost":"0.085823 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":25,"timeTaken":"133.43s","timestamp":"2025-07-31T13:45:25.763Z"}
{"avgCostPerNFT":"0.003433 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":609,"successRate":"100.0%","timestamp":"2025-07-31T13:45:25.766Z","totalCost":"0.085823 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"147.85s"}
{"batchSize":25,"estimatedBatches":1,"level":"info","message":"🚀 Starting Core NFT minting process","timestamp":"2025-07-31T14:17:28.027Z","totalNFTs":25}
{"level":"info","message":"💰 Processing Core NFTs sequentially for minimum cost","timestamp":"2025-07-31T14:17:28.029Z"}
{"batchNumber":1,"level":"info","message":"📦 Starting batch 1/1","nftsInBatch":25,"progress":"0.0%","timestamp":"2025-07-31T14:17:28.030Z","totalBatches":1}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:28.992Z"}
{"assetAddress":"HjjZmoCEybxyFRB6wovLKYYtHU7HJnrGu3cMjeWnidZf","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #1","timeTaken":"3.33s","timestamp":"2025-07-31T14:17:31.682Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:32.094Z"}
{"assetAddress":"5BwX5nsg4ncRkTJbxkMXWvJY8B1oommMYxvH4qVkhNHZ","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #2","timeTaken":"2.77s","timestamp":"2025-07-31T14:17:34.748Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:35.020Z"}
{"assetAddress":"CpAuqJZruoeyM9gGsgh3s5zna21e5fCc7dbRKhV7AFJ2","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #3","timeTaken":"2.71s","timestamp":"2025-07-31T14:17:37.795Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:38.109Z"}
{"assetAddress":"5EPmgndB9AEBspWvjbKwLVpPRdXqwgZ5HcEuEc4tVpqE","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #4","timeTaken":"3.62s","timestamp":"2025-07-31T14:17:41.683Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:41.953Z"}
{"assetAddress":"7QMr4HoVYdmgSyU4FCzZcT5tJfBQyuBkWxxBoSJuWqmX","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #5","timeTaken":"2.93s","timestamp":"2025-07-31T14:17:44.872Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:45.131Z"}
{"assetAddress":"4mLqVc3KDUUGLbjtEPJTs3DrMf7PPfHuRJBjzkNBx3VR","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #6","timeTaken":"3.98s","timestamp":"2025-07-31T14:17:49.139Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:49.524Z"}
{"assetAddress":"QQCnaCSc7PFdPQVsoUrXfpU5FnmRYKzzxffbrhNZ4u8","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #7","timeTaken":"3.47s","timestamp":"2025-07-31T14:17:52.919Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:53.197Z"}
{"assetAddress":"2BKFvGCsM16a3neC7fQ75KC3KXG1a67bvZu5CU4f74E3","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #8","timeTaken":"5.20s","timestamp":"2025-07-31T14:17:58.525Z"}
{"level":"warn","message":"Metadata URI too long (291), using minimal version","timestamp":"2025-07-31T14:17:58.864Z"}
{"assetAddress":"4JcRXVaE8BrLCgEtcck9J2YVQLN9zgZXXQ3ef7uX2beV","cost":"0.003424 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #9","timeTaken":"2.83s","timestamp":"2025-07-31T14:18:01.641Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:01.967Z"}
{"assetAddress":"DMGMMbZatgsEoWdb6oLYy323TFofAq6Ceuc1k5DGWPeQ","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #10","timeTaken":"3.19s","timestamp":"2025-07-31T14:18:05.129Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:05.439Z"}
{"assetAddress":"AVmmpinD6h3MJE2u9ACUpygivW98NynazM3hUomH6vhF","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #11","timeTaken":"3.87s","timestamp":"2025-07-31T14:18:09.435Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:09.711Z"}
{"assetAddress":"CfLrZvN3xhxVP6PhHM8YBuzwUeMBt43Fi9jQoB288rKG","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #12","timeTaken":"3.48s","timestamp":"2025-07-31T14:18:13.223Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:13.520Z"}
{"assetAddress":"A5FtHaM4j6WVxe9j33AwpX55pxFCfuc6YuAqKzVyHXxy","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #13","timeTaken":"2.49s","timestamp":"2025-07-31T14:18:16.079Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:16.346Z"}
{"assetAddress":"Hn2bzCXg158XQpnBpb4TTGMFTd3ZB8zsbfqAGBHbuiut","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #14","timeTaken":"6.07s","timestamp":"2025-07-31T14:18:22.413Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:22.688Z"}
{"assetAddress":"3q6WQvLh6uLUkqvcj83Y46TQ9dgP1oUJFtifSHFgGWZz","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #15","timeTaken":"3.49s","timestamp":"2025-07-31T14:18:27.173Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:27.486Z"}
{"assetAddress":"DCdk3USun6BQKyW3c8x9NpQQS7v2YoAtcpMhmUBTMSc7","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #16","timeTaken":"2.91s","timestamp":"2025-07-31T14:18:30.433Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:30.743Z"}
{"assetAddress":"kLjFrvmq1zBikGfnAMW6uGcqQqHWU1CPJFzmdaLaLnV","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #17","timeTaken":"4.41s","timestamp":"2025-07-31T14:18:35.120Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:35.444Z"}
{"assetAddress":"F26yMAK9rBQakKvNa9nsPryBvCmk6gq3uaREcxacQa3W","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #18","timeTaken":"2.78s","timestamp":"2025-07-31T14:18:38.289Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:38.699Z"}
{"assetAddress":"G8y453GgTaAoWvQFcJqGFvxydMUh4qsdL6Yy8NuUM5uX","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #19","timeTaken":"3.86s","timestamp":"2025-07-31T14:18:42.513Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:42.826Z"}
{"assetAddress":"2jzyvBicms7Gz79zmTU7oDCmYKeYciiN1Kemq3eCfLmt","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #20","timeTaken":"3.80s","timestamp":"2025-07-31T14:18:46.585Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:46.880Z"}
{"assetAddress":"cbYE2Kc4W9kyxNH1wtoV2W3eCh7mQbp29iCfDhgQRmb","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #21","timeTaken":"2.96s","timestamp":"2025-07-31T14:18:49.818Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:50.081Z"}
{"assetAddress":"C4TSrqAqdStwcfQc3mNbux5E4ATKGKtJT7pRRVKPgwNt","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #22","timeTaken":"3.16s","timestamp":"2025-07-31T14:18:53.363Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:53.650Z"}
{"assetAddress":"25EyDeyQvKeQbD2WigDwduTYDUWG1XArKpGLfSPsze17","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #23","timeTaken":"3.33s","timestamp":"2025-07-31T14:18:56.982Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:18:57.359Z"}
{"assetAddress":"3FTFhTd6CYxDmvqEzunqK2K9BqMNqKs5AnCZdHz1Edgk","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #24","timeTaken":"3.47s","timestamp":"2025-07-31T14:19:00.736Z"}
{"level":"warn","message":"Metadata URI too long (294), using minimal version","timestamp":"2025-07-31T14:19:01.060Z"}
{"assetAddress":"BH5xCvYJB7zMpiMeyEAkgf3WsBmi2yxPQFuqycpr3geJ","cost":"0.003438 SOL","level":"info","message":"✅ Successfully minted Core NFT: Regular NFT #25","timeTaken":"2.99s","timestamp":"2025-07-31T14:19:04.016Z"}
{"cost":"0.085823 SOL","failed":0,"level":"info","message":"✅ Batch 1 completed","successRate":"100.0%","successful":25,"timeTaken":"87.10s","timestamp":"2025-07-31T14:19:04.018Z"}
{"avgCostPerNFT":"0.003433 SOL","level":"info","message":"🎉 Core NFT minting process completed!","nftsPerHour":938,"successRate":"100.0%","timestamp":"2025-07-31T14:19:04.020Z","totalCost":"0.085823 SOL","totalFailed":0,"totalSuccessful":25,"totalTime":"95.99s"}
{"level":"warn","message":"All RPC endpoints are unhealthy!","timestamp":"2025-07-31T14:33:24.390Z"}
