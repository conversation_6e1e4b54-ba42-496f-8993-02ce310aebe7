# 🚀 Simple NFT Minter - Client Guide

## 📋 What This Does
This tool mints Solana NFTs directly to your wallet with minimal cost and maximum simplicity.

## 🎯 Quick Start (3 Steps)

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Configure Your Settings
Open `client-config.js` and change these values:

```javascript
// 👤 YOUR WALLET INFORMATION
WALLET_PRIVATE_KEY: [your, private, key, array],

// 🎯 WHERE TO SEND THE NFTs  
CLIENT_WALLET_ADDRESS: 'your-wallet-address-here',

// 📦 HOW MANY NFTs TO MINT
NUMBER_OF_NFTS: 25,

// 🌐 NETWORK ('devnet' for testing, 'mainnet-beta' for real)
NETWORK: 'devnet',
```

### Step 3: Run the Minter
```bash
npm start
```

That's it! 🎉

## 📁 Files You Need to Know

### ✅ Files You SHOULD Modify:
- **`client-config.js`** - Your main configuration file (ONLY file you need to change!)

### ❌ Files You DON'T Need to Touch:
- `client-mint.js` - The minting script (don't modify)
- `package.json` - Dependencies (don't modify)
- All other files - Advanced features (don't modify)

## 🔧 Configuration Options

### Required Settings (Must Change):
- **`WALLET_PRIVATE_KEY`** - Your wallet's private key as an array
- **`CLIENT_WALLET_ADDRESS`** - Where the NFTs should be sent
- **`NUMBER_OF_NFTS`** - How many NFTs to mint

### Optional Settings (Can Change):
- **`NETWORK`** - 'devnet' (free testing) or 'mainnet-beta' (real deployment)
- **`MAX_COST_PER_NFT`** - Maximum cost per NFT (default: 0.009 SOL)
- **`COLLECTION_NAME`** - Your collection name
- **`NFT_TEMPLATE`** - NFT metadata template

### Advanced Settings (Usually Don't Change):
- **`BATCH_SIZE`** - How many NFTs to process at once
- **`MINT_DELAY`** - Delay between mints (milliseconds)
- **`GENERATE_REPORT`** - Create HTML report (true/false)

## 🌐 Network Guide

### Devnet (Testing - Recommended First)
- **Cost**: FREE (uses fake SOL)
- **Purpose**: Testing your setup
- **Setting**: `NETWORK: 'devnet'`
- **Get Test SOL**: https://faucet.solana.com/

### Mainnet-Beta (Real Deployment)
- **Cost**: Real SOL (~0.003 SOL per NFT)
- **Purpose**: Real NFT deployment
- **Setting**: `NETWORK: 'mainnet-beta'`
- **Need**: Real SOL in your wallet

## 🔑 Getting Your Private Key

### From Phantom Wallet:
1. Open Phantom
2. Settings → Security & Privacy
3. Export Private Key
4. Copy the array format: `[123,456,789,...]`

### From Solflare:
1. Open Solflare
2. Settings → Export Wallet
3. Copy private key as array

### From CLI:
```bash
solana-keygen pubkey ~/.config/solana/id.json --outfile
```

## 💰 Cost Estimation

### Devnet (Testing):
- **Cost per NFT**: FREE
- **Total for 25 NFTs**: FREE

### Mainnet-Beta (Real):
- **Cost per NFT**: ~0.003 SOL
- **Total for 25 NFTs**: ~0.075 SOL
- **Budget Target**: ≤0.009 SOL per NFT ✅

## 📊 What Happens After Minting

1. **Console Output**: Real-time progress updates
2. **HTML Report**: Detailed report with all NFT links
3. **Wallet Update**: NFTs appear in your wallet
4. **Explorer Links**: Verify ownership on Solana Explorer

## 🔍 Verifying Your NFTs

1. **Check Your Wallet**: NFTs should appear automatically
2. **Switch Network**: Make sure wallet is on correct network
3. **Refresh Wallet**: Sometimes takes a few minutes
4. **Use Explorer**: Click links in the HTML report

## ❌ Troubleshooting

### "Insufficient funds" Error:
- Add more SOL to your wallet
- For devnet: Use https://faucet.solana.com/
- For mainnet: Buy SOL and send to your wallet

### "Invalid private key" Error:
- Check your private key format in `client-config.js`
- Make sure it's an array: `[123,456,789,...]`
- No quotes around the numbers

### "Network error" Error:
- Check your internet connection
- Try again in a few minutes
- Switch networks if needed

### NFTs not showing in wallet:
- Wait a few minutes and refresh
- Make sure wallet is on correct network
- Check the HTML report for explorer links

## 🎯 Success Checklist

- [ ] `npm install` completed successfully
- [ ] `client-config.js` updated with your settings
- [ ] Wallet has enough SOL for minting
- [ ] Network setting matches your wallet
- [ ] `npm start` runs without errors
- [ ] HTML report generated
- [ ] NFTs visible in your wallet

## 💡 Tips for Success

1. **Start with Devnet**: Always test first!
2. **Small Batches**: Start with 5-10 NFTs to test
3. **Check Balance**: Ensure enough SOL before starting
4. **Save Config**: Keep a backup of your `client-config.js`
5. **Monitor Progress**: Watch the console output

## 🆘 Need Help?

If you encounter issues:

1. **Check the console output** for specific error messages
2. **Verify your configuration** in `client-config.js`
3. **Try devnet first** before mainnet-beta
4. **Start with fewer NFTs** to test
5. **Check your wallet balance** and network

## 🎉 Success!

When everything works:
- ✅ NFTs minted successfully
- ✅ Sent to your wallet address
- ✅ Under budget (≤0.009 SOL per NFT)
- ✅ HTML report generated
- ✅ Ready to use or trade!

---

**Remember**: Only modify `client-config.js` - everything else is handled automatically! 🚀
