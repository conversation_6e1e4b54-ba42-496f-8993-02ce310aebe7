"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreNFTMinter = void 0;
const web3_js_1 = require("@solana/web3.js");
const umi_1 = require("@metaplex-foundation/umi");
const umi_bundle_defaults_1 = require("@metaplex-foundation/umi-bundle-defaults");
const mpl_core_1 = require("@metaplex-foundation/mpl-core");
const p_retry_1 = __importDefault(require("p-retry"));
const logger_1 = require("./utils/logger");
const rpc_manager_1 = require("./utils/rpc-manager");
const config_1 = require("./config");
class CoreNFTMinter {
    constructor(privateKey, options) {
        this.logger = logger_1.Logger.getInstance();
        this.rpcManager = rpc_manager_1.RPCManager.getInstance();
        this.config = config_1.Config.getInstance();
        this.wallet = web3_js_1.Keypair.fromSecretKey(new Uint8Array(JSON.parse(privateKey)));
        this.connection = this.rpcManager.getConnection();
        this.stats = {
            startTime: Date.now(),
            totalNFTs: 0,
            processedNFTs: 0,
            successfulMints: 0,
            failedMints: 0,
            totalCost: 0,
        };
    }
    async mintSingleCoreNFT(config, options, recipientAddress) {
        try {
            const startTime = Date.now();
            const initialBalance = await this.connection.getBalance(this.wallet.publicKey);
            // Create UMI instance for Metaplex Core
            const umi = (0, umi_bundle_defaults_1.createUmi)(this.connection.rpcEndpoint);
            umi.use((0, mpl_core_1.mplCore)());
            // Convert Solana keypair to UMI format
            const umiKeypair = umi.eddsa.createKeypairFromSecretKey(this.wallet.secretKey);
            umi.use((0, umi_1.keypairIdentity)(umiKeypair));
            // Generate asset signer
            const asset = (0, umi_1.generateSigner)(umi);
            this.logger.debug(`Minting Core NFT: ${config.name}`);
            const result = await (0, p_retry_1.default)(async () => {
                // Create NFT directly with client as owner if specified
                const createTx = await (0, mpl_core_1.create)(umi, {
                    asset,
                    name: config.name || `NFT #${Date.now()}`,
                    uri: await this.createMetadataUri(config),
                    ...(recipientAddress && { owner: (0, umi_1.publicKey)(recipientAddress) })
                });
                const signature = await createTx.sendAndConfirm(umi);
                this.logger.debug(`NFT created for: ${recipientAddress || 'minting wallet'}`);
                return { signature, asset: asset.publicKey };
            }, {
                retries: 1,
                minTimeout: 100,
                onFailedAttempt: (error) => {
                    this.logger.warn(`Core mint attempt failed for ${config.name}: ${error.message}`);
                }
            });
            const endTime = Date.now();
            const timeTaken = (endTime - startTime) / 1000;
            // Calculate actual cost
            const finalBalance = await this.connection.getBalance(this.wallet.publicKey);
            const actualCost = (initialBalance - finalBalance) / web3_js_1.LAMPORTS_PER_SOL;
            this.logger.info(`✅ Successfully minted Core NFT: ${config.name}`, {
                assetAddress: result.asset.toString(),
                cost: `${actualCost.toFixed(6)} SOL`,
                timeTaken: `${timeTaken.toFixed(2)}s`
            });
            return {
                success: true,
                mintAddress: result.asset.toString(),
                signature: result.signature.toString(),
                cost: actualCost,
                timeTaken,
                metadata: {
                    name: config.name,
                    description: config.description,
                    image: `https://via.placeholder.com/512x512.png?text=${encodeURIComponent(config.name)}`,
                    attributes: config.attributes,
                    collection: config.collection,
                    properties: {
                        files: [
                            {
                                uri: `https://via.placeholder.com/512x512.png?text=${encodeURIComponent(config.name)}`,
                                type: "image/png"
                            }
                        ],
                        category: "image"
                    }
                }
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown minting error';
            this.logger.error(`Failed to mint Core NFT: ${config.name}`, { error: errorMessage });
            return {
                success: false,
                error: errorMessage,
                cost: 0,
                timeTaken: 0
            };
        }
    }
    async batchMintCoreNFTs(nftConfigs, options, recipientAddress) {
        const startTime = Date.now();
        this.stats.totalNFTs = nftConfigs.length;
        this.stats.startTime = startTime;
        this.logger.info('🚀 Starting Core NFT minting process', {
            totalNFTs: nftConfigs.length,
            batchSize: options.batchSize,
            estimatedBatches: Math.ceil(nftConfigs.length / options.batchSize)
        });
        const results = [];
        const errors = [];
        let totalCost = 0;
        // Process sequentially for minimum cost
        this.logger.info('💰 Processing Core NFTs sequentially for minimum cost');
        for (let i = 0; i < nftConfigs.length; i++) {
            const config = nftConfigs[i];
            const batchNumber = Math.floor(i / options.batchSize) + 1;
            const totalBatches = Math.ceil(nftConfigs.length / options.batchSize);
            if (i % options.batchSize === 0) {
                this.logger.info(`📦 Starting batch ${batchNumber}/${totalBatches}`, {
                    batchNumber,
                    totalBatches,
                    nftsInBatch: Math.min(options.batchSize, nftConfigs.length - i),
                    progress: `${((i / nftConfigs.length) * 100).toFixed(1)}%`
                });
            }
            const result = await this.mintSingleCoreNFT(config, options, recipientAddress);
            results.push(result);
            if (result.success) {
                this.stats.successfulMints++;
                totalCost += result.cost || 0;
            }
            else {
                this.stats.failedMints++;
                if (result.error) {
                    errors.push(result.error);
                }
            }
            this.stats.processedNFTs++;
            // Log batch completion
            if ((i + 1) % options.batchSize === 0 || i === nftConfigs.length - 1) {
                const batchResults = results.slice(Math.max(0, i - options.batchSize + 1), i + 1);
                const batchSuccessful = batchResults.filter(r => r.success).length;
                const batchFailed = batchResults.filter(r => !r.success).length;
                const batchCost = batchResults.reduce((sum, r) => sum + (r.cost || 0), 0);
                const batchTime = batchResults.reduce((sum, r) => sum + (r.timeTaken || 0), 0);
                this.logger.info(`✅ Batch ${batchNumber} completed`, {
                    successful: batchSuccessful,
                    failed: batchFailed,
                    cost: `${batchCost.toFixed(6)} SOL`,
                    timeTaken: `${batchTime.toFixed(2)}s`,
                    successRate: `${((batchSuccessful / batchResults.length) * 100).toFixed(1)}%`
                });
            }
        }
        const endTime = Date.now();
        const totalTime = (endTime - startTime) / 1000;
        this.stats.endTime = endTime;
        this.stats.totalCost = totalCost;
        const successfulMints = results.filter(r => r.success).length;
        const failedMints = results.filter(r => !r.success).length;
        const averageCostPerNFT = successfulMints > 0 ? totalCost / successfulMints : 0;
        const nftsPerHour = totalTime > 0 ? (successfulMints / totalTime) * 3600 : 0;
        this.logger.info('🎉 Core NFT minting process completed!', {
            totalSuccessful: successfulMints,
            totalFailed: failedMints,
            totalCost: `${totalCost.toFixed(6)} SOL`,
            avgCostPerNFT: `${averageCostPerNFT.toFixed(6)} SOL`,
            totalTime: `${totalTime.toFixed(2)}s`,
            nftsPerHour: Math.round(nftsPerHour),
            successRate: `${((successfulMints / nftConfigs.length) * 100).toFixed(1)}%`
        });
        return {
            totalNFTs: nftConfigs.length,
            successfulMints,
            failedMints,
            totalCost,
            averageCostPerNFT,
            totalTime,
            results,
            errors
        };
    }
    async createMetadataUri(config) {
        try {
            // OPTIMIZED metadata JSON - compact but complete
            const metadata = {
                name: config.name.substring(0, 32), // Limit name length
                description: config.description.substring(0, 100), // Limit description
                image: "https://i.imgur.com/placeholder.png", // Short image URL
                attributes: config.attributes.slice(0, 3).map(attr => ({
                    trait_type: attr.trait_type.substring(0, 15), // Limit trait names
                    value: attr.value.substring(0, 15) // Limit trait values
                }))
                // Remove properties and collection to reduce size
            };
            // Convert to compact data URI
            const metadataJson = JSON.stringify(metadata);
            const metadataUri = `data:application/json,${metadataJson}`; // No base64 to save bytes
            // Check if URI is too long for transaction
            if (metadataUri.length > 200) {
                this.logger.warn(`Metadata URI too long (${metadataUri.length}), using minimal version`);
                return `data:application/json,{"name":"${config.name.substring(0, 20)}","image":""}`;
            }
            this.logger.debug(`Created optimized metadata URI for: ${config.name}`);
            return metadataUri;
        }
        catch (error) {
            this.logger.warn(`Failed to create metadata URI for ${config.name}, using minimal URI`);
            return `data:application/json,{"name":"${config.name.substring(0, 20)}","image":""}`;
        }
    }
    async validateWallet() {
        try {
            const balance = await this.connection.getBalance(this.wallet.publicKey);
            const balanceInSOL = balance / web3_js_1.LAMPORTS_PER_SOL;
            return {
                isValid: true,
                balance: balanceInSOL,
                publicKey: this.wallet.publicKey.toBase58(),
                errors: []
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';
            return {
                isValid: false,
                balance: 0,
                publicKey: this.wallet.publicKey.toBase58(),
                errors: [errorMessage]
            };
        }
    }
    async estimateCosts(nftCount) {
        // METAPLEX CORE cost estimates - single account design
        // Based on research: Core costs 0.0029 SOL vs 0.022 SOL for Token Metadata
        const baseMintCost = 0.003; // Metaplex Core single account cost
        const storageCost = 0; // Zero storage cost
        const priorityFeeCost = 0; // Zero priority fees
        const costPerNFT = baseMintCost + storageCost + priorityFeeCost;
        const totalEstimatedCost = costPerNFT * nftCount;
        return {
            totalEstimatedCost,
            costPerNFT,
            breakdown: {
                mintCost: baseMintCost,
                storageCost,
                priorityFees: priorityFeeCost
            }
        };
    }
}
exports.CoreNFTMinter = CoreNFTMinter;
//# sourceMappingURL=core-nft-minter.js.map