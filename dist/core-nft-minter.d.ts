import { NFTConfig, MintingOptions, MintResult, BatchMintResult } from './types';
export declare class CoreNFTMinter {
    private connection;
    private wallet;
    private logger;
    private rpcManager;
    private config;
    private stats;
    constructor(privateKey: string, options: MintingOptions);
    mintSingleCoreNFT(config: NFTConfig, options: MintingOptions, recipientAddress?: string): Promise<MintResult>;
    batchMintCoreNFTs(nftConfigs: NFTConfig[], options: MintingOptions, recipientAddress?: string): Promise<BatchMintResult>;
    private createMetadataUri;
    validateWallet(): Promise<{
        isValid: boolean;
        balance: number;
        publicKey: string;
        errors: string[];
    }>;
    estimateCosts(nftCount: number): Promise<{
        totalEstimatedCost: number;
        costPerNFT: number;
        breakdown: {
            mintCost: number;
            storageCost: number;
            priorityFees: number;
        };
    }>;
}
//# sourceMappingURL=core-nft-minter.d.ts.map